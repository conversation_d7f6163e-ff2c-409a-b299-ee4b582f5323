body {
  margin: 0;
  padding: 0 10px;
  font-size: 16px;
  text-align: center;
  font-family: "Arial", sans-serif;
}

h1 {
  font-size: 12px;
}

.menu {
  display: flex;
}

.menu span {
  cursor: pointer;
  padding: 3px 0;
  flex-basis: auto;
  flex-grow: 1;
  text-align: center;
  font-size: 12px;
  border-radius: 5px;
  color: #fff;
  background-color: #000000;
  margin: 0.1em;
}

.menu span:hover {
  background: #4a4a4a;
}

span.menu-active {
  background: #779dd9;
}

.digits {
  margin-bottom: 5px;
  display: flex;
}

.digits span {
  padding: 3px 0;
  flex-basis: auto;
  flex-grow: 1;
  text-align: center;
  font-size: 16px;
  /* border-radius: 5px; */
  color: #fff;
  border-radius: 0.2em;
  margin: 1px;
}

.digits_moved_down {
  background-color: #c03;
}

.digits_moved_up {
  background-color: #29abe2;
}

.headcol {
  margin-bottom: 2px;
  display: flex;
}

.headcol span {
  padding: 2px 0;
  flex-basis: auto;
  flex-grow: 1;
  text-align: center;
  font-size: 0px;
  /* border-radius: 5px; */
  color: #fff;
}

.Head_moved_down {
  background-color: #c03;
}

.Head_moved_up {
  background-color: #29abe2;
}

.Head_moved_mid {
  background-color: #32cd32;
}

.placecol {
  margin-bottom: 10px;
  display: flex;
}

.placecol span {
  padding: 2px 0;
  flex-basis: auto;
  flex-grow: 1;
  text-align: center;
  font-size: 0px;
  /* border-radius: 5px; */
  color: #fff;
}

.Place_moved_down {
  background-color: #c03;
}

.Place_moved_up {
  background-color: #29abe2;
}

.Place_moved_none {
  background-color: #fff;
}

.chartContainer {
  /* margin-bottom: 15px; */
  min-height: 180px;
  min-width: 125px;
}

.chartContainerDigit {
  min-height: 115px;
  min-width: 125px;
}

input {
  font-family: monospace;
  font-weight: bold;
  border: 1px solid #909090;
  /* border-radius: 4px; */
  outline: none;
}

#token {
  padding: 0.2em;
  border-right: transparent;
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  /* width: 8em; */
}

#tokenbtn {
  color: white;
  background: black;
  position: relative;
  left: -2em;
  padding: 0.2em 0.5em;
  border-radius: 0;

}

.top-lbl {
  margin: 0;
  margin-left: 0.5em;
  font-size: 1.5em;
}

.row {
  width: 100%;
  ;
}

.column {
  float: left;
  width: 33.33%;
}

/* Clear floats after the columns */
.row:after {
  content: "";
  display: table;
  clear: both;
}


.textbox-btm {
  padding-left: 0.5em;
  border-radius: 5px;
  width: 120%;
}

.risebtn {
  background: linear-gradient(145deg, #3498db, #2980b9);
  border: none;
  border-radius: 4px;
  color: #ffffff;
  cursor: pointer;
  display: inline-block;
  font-family: 'Arial', sans-serif;
  padding: 1px 20px;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s ease;
  width: 60%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}


.risebtn:active {
  background: linear-gradient(145deg, #2980b9, #23689e);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.risebtn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.4);
}


.fallbtn {
  /* box-shadow: 0px 10px 14px -7px #b07a41; */
  background: linear-gradient(to bottom, #c94e4e 5%, #c94e4e 100%);
  background-color: #c94e4e;
  border-radius: 4px;
  border: 1px solid #ad4949;
  display: inline-block;
  cursor: pointer;
  color: #ffffff;
  font-family: Arial;
  font-size: 13px;
  /* font-weight: bold; */
  width: 60%;
  text-decoration: none;
  /* text-shadow: 0px 1px 0px #c75732; */
}

.fallbtn:hover {
  background: linear-gradient(to bottom, #c94e4e 5%, #c94e4e 100%);
  background-color: #c94e4e;
}

.fallbtn:active {
  position: relative;
  top: 1px;
}

.button-active {
  color: #000 !important;
  background-color: #ccc !important
}


/* ------------------------------------------------------------------  */

:root {
  --color-white-100: hsl(206, 5%, 98%);
  --color-white-200: hsl(206, 5%, 90%);
  --color-white-300: hsl(206, 5%, 80%);
  --color-white-400: hsl(206, 5%, 65%);
  --color-white-500: hsl(206, 5%, 50%);
  --color-black-100: hsl(213, 23%, 8%);
  --color-black-200: hsl(214, 21%, 6%);
  --color-black-300: hsl(210, 21%, 6%);
  --color-black-400: hsl(216, 22%, 4%);
  --color-black-500: hsl(220, 18%, 3%);
  --color-pink-100: hsl(326, 10%, 32%);
  --color-pink-200: hsl(336, 3%, 29%);
  --color-pink-300: hsl(330, 4%, 9%);
  --color-pink-400: hsl(328, 11%, 26%);
  --color-pink-500: hsl(336, 5%, 19%);
  --shadow-small: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

*,
*::before,
*::after {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  list-style: none;
  list-style-type: none;
  text-decoration: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

html {
  font-size: 100%;
  box-sizing: inherit;
  scroll-behavior: smooth;
  height: -webkit-fill-available;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  height: -webkit-fill-available;
  color: var(--color-black-500);
  background-color: var(--color-white-100);
}

a,
button {
  cursor: pointer;
  border: none;
  outline: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background: none;
  box-shadow: none;
}

img,
video {
  display: block;
  max-width: 100%;
  height: auto;
  -o-object-fit: cover;
  object-fit: cover;
}

.container {
  max-width: 75rem;
  height: auto;
  margin: 0 auto;
  padding: 0 1.25rem;
}

.brand {
  font-family: inherit;
  font-size: 1.625rem;
  font-weight: 700;
  line-height: 1.25;
  letter-spacing: -1px;
  text-transform: uppercase;
  color: var(--color-pink-500);
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: auto;
  z-index: 100;
  margin: 0 auto;
  box-shadow: var(--shadow-medium);
  background-color: var(--color-white-100);
}

.wrapper {
  max-width: inherit;
  display: flex;
  align-items: center;
  justify-content: space-between;
  row-gap: 2rem;
  width: 100%;
  height: 3.5rem;
  margin: 0 auto;
}

@media screen and (max-width: 992px) {
  .navbar {
    position: fixed;
    top: 0;
    left: -100%;
    width: 75%;
    height: 100%;
    z-index: 10;
    opacity: 0;
    overflow-y: auto;
    visibility: hidden;
    box-shadow: var(--shadow-medium);
    background-color: var(--color-white-100);
    transition: all 0.5s ease;
  }

  .navbar.active {
    left: 0rem;
    opacity: 1;
    visibility: visible;
  }
}

.menu-item {
  position: relative;
  display: inline-block;
  margin-left: 1.5rem;
}

.menu-link {
  display: flex;
  justify-content: center;
  align-items: center;
  -moz-column-gap: 0.25rem;
  column-gap: 0.25rem;
  font-family: inherit;
  font-size: 1rem;
  font-weight: 500;
  line-height: inherit;
  cursor: pointer;
  text-transform: capitalize;
  color: var(--color-black-500);
  transition: all 0.3s ease-in-out;
}

.menu-link>i.bx {
  font-size: 1.35rem;
  line-height: 1.5;
  color: inherit;
}

.menu-link:hover {
  outline: none;
  color: var(--color-pink-500);
}

@media only screen and (min-width: 993px) {
  .menu-dropdown:hover>.submenu {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

@media only screen and (max-width: 992px) {
  .menu {
    width: 100%;
    height: auto;
    padding: 1rem 0;
    display: block;
  }

  .menu-item {
    display: block;
    margin: 0 auto;
    text-align: left;
    font-size: 1.2em;
    padding: 0.5em;
  }

  .menu-link {
    justify-content: space-between;
    padding: 0.5rem 1.25rem;
  }

  .market-menue {
    display: flex !important;
    margin-top: -4.5em !important
  }

  .top-lbl {
    font-size: 1.2em;
  }

  #opration-cosole {
    width: 60% !important;
  }

  #output {
    width: 40% !important;
  }

  .w3-bar .w3-button {
    font-size: 0.9em;
  }

}

.msgoutput {
  font-size: 0.85em;
  float: left;
  padding: 10px;
  overflow-y: scroll;
  width: 60%;
  height: 150px;
  max-height: 300px;
  display: inline-block;
  text-align: left;
  border: 1px solid #a3a3a3;
  border-radius: 5px;
  border-right: none;
}

@media only screen and (min-width: 992px) {
  #mobile-login {
    display: none !important;
  }
}

@media only screen and (max-width: 600px) {
  .top-lbl {
    font-size: 1em;
  }

  #opration-cosole {
    width: 100% !important;
    font-size: 0.9em !important;
  }

  #output {
    width: 100% !important;
    margin-top: 1em !important;
    border: 1px solid #a3a3a3;

  }

  .chartContainerDigit {
    min-height: 110px !important
  }
}


.submenu {
  position: absolute;
  top: 2.35rem;
  left: -2rem;
  min-width: 13rem;
  height: auto;
  padding: 0 1rem 1rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(1rem);
  border-radius: 0 0 0.25rem 0.25rem;
  border-top: 2px solid var(--color-pink-400);
  box-shadow: var(--shadow-medium);
  background-color: var(--color-white-100);
  transition: all 0.3s ease-in-out;
}

.submenu-item {
  display: block;
  margin-top: 0.75rem;
}

.submenu-link {
  font-family: inherit;
  font-size: 1rem;
  font-weight: 500;
  line-height: inherit;
  cursor: pointer;
  color: var(--color-black-500);
  transition: all 0.35s ease;
}

.submenu-link:hover {
  outline: none;
  color: var(--color-pink-500);
}

@media only screen and (max-width: 992px) {
  .submenu {
    position: relative;
    top: -0.5rem;
    left: 2.5rem;
    width: 100%;
    max-height: 0;
    padding: 0px;
    border: none;
    outline: none;
    opacity: 1;
    overflow: hidden;
    visibility: visible;
    transform: translateY(0px);
    box-shadow: none;
    background: transparent;
  }
}

.burger {
  position: relative;
  display: none;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.75rem;
  height: 1rem;
  opacity: 0;
  visibility: hidden;
  background: transparent;
}

.burger-line {
  position: absolute;
  display: block;
  left: 0;
  width: 100%;
  height: 2px;
  opacity: 1;
  border: none;
  outline: none;
  background: var(--color-black-500);
}

.burger-line:nth-child(1) {
  top: 0px;
}

.burger-line:nth-child(2) {
  top: 0.5rem;
}

.burger-line:nth-child(3) {
  top: 1rem;
}

@media only screen and (max-width: 992px) {
  .burger {
    display: block;
    opacity: 1;
    visibility: visible;
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9;
  opacity: 0;
  visibility: hidden;
  transition: all 0.35s ease-in-out;
  background-color: rgba(0, 0, 0, 0.65);
}

@media only screen and (max-width: 992px) {
  .overlay.active {
    display: block;
    opacity: 1;
    visibility: visible;
  }
}

/* CSS */
.button-3 {
  width: 10em;
  appearance: none;
  background-color: #2ea44f;
  border: 1px solid rgba(27, 31, 35, .15);
  border-radius: 6px;
  box-shadow: rgba(27, 31, 35, .1) 0 1px 0;
  box-sizing: border-box;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-family: -apple-system, system-ui, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  padding: 6px 16px;
  position: relative;
  text-align: center;
  text-decoration: none;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  vertical-align: middle;
  white-space: nowrap;
}

.button-3:focus:not(:focus-visible):not(.focus-visible) {
  box-shadow: none;
  outline: none;
}

.button-3:hover {
  background-color: #2c974b;
}

.button-3:focus {
  box-shadow: rgba(46, 164, 79, .4) 0 0 0 3px;
  outline: none;
}

.button-3:disabled {
  background-color: #94d3a2;
  border-color: rgba(27, 31, 35, .1);
  color: rgba(255, 255, 255, .8);
  cursor: default;
}

.button-3:active {
  background-color: #298e46;
  box-shadow: rgba(20, 70, 32, .2) 0 1px 0 inset;
}

.img-text-men {
  font-size: 1.2em;
  padding: 0.5em;
}

/* -------------------------------------------------- */


.select-mrkt {
  appearance: none;
  /* safari */
  -webkit-appearance: none;
  /* other styles for aesthetics */
  width: 60%;
  font-size: 1.15rem;
  padding: 0.675em 6em 0.675em 1em;
  background-color: #fff;
  border: 1px solid #caced1;
  border-radius: 0.25rem;
  cursor: pointer;
}

.w3-bar .w3-button {
  border-radius: 5px;
  margin: 0 0.1em;
  background: #67636a;
  padding: 0.2em 0.5em !important;
}

.w3-bar {
  padding: 0.2em 0 !important;
  background-color: #fff !important;
}

.w3-container {
  border-top: 1px solid #caced1;
}


.tbl-rw {
  padding: 0.5em 0;
  font-size: 0.9em;
}


#opration-cosole {
  text-align: left;
  width: 40%;
  height: 150px;
  max-height: 300px;
  display: inline-block;
  border: 1px solid #a3a3a3;
  border-radius: 5px;
}


.disabledbtn {
  pointer-events: none;
  cursor: default;
  text-decoration: none;
  opacity: 0.6;
}

.live-indicator-analyzing {
  width: 1.4em;
  position: initial;
  float: right;
  margin-left: 1em;
  display: none;
  margin-right: 0.2em;
}

/* _________________________________________ */

.notyf__toast--dismissible .notyf__wrapper {
  font-size: 0.8em;
  padding: 0.5em;
  padding-left: 0;
}

.setting-titl {
  color: #3a3a3a;
  font-weight: 550;
  padding-left: 1em;
  float: left;
}

.notiy-icon {
  color: #fff !important;
  font-size: 1.5em !important;
}

.span-text-safe {
  vertical-align: top;
}

.span-text-safe:before {
  content: "Safe Sell";
}


.market-condition {
  display: inline-block;
  padding: 0 8px;
  border-radius: 15px;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 12px;
  transition: all 0.5s ease;
}

.market-condition.very-bad {
  background-color: #FF4136;
  color: white;
}

.market-condition.bad {
  background-color: #FF851B;
  color: white;
}

.market-condition.neutral {
  background-color: #FFDC00;
  color: black;
}

.market-condition.good {
  background-color: #2ECC40;
  color: white;
}

.market-condition.very-good {
  background-color: #3D9970;
  color: white;
}



.w3-bar {

  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.w3-bar-item.w3-button {
  padding: 10px 10px;

  border: none;
  border-radius: 4px;
  background-color: #406781;
  color: white;
  font-family: 'Arial', sans-serif;
  font-size: 1em;
  font-weight: 400;
  /* text-transform: uppercase; */
  transition: all 0.2s ease;
  cursor: pointer;
  outline: none;
  text-decoration: none;
}

.w3-bar-item.w3-button:hover {
  background-color: #2980b9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.w3-bar-item.w3-button.button-active {
  background-color: #2c3e50;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Responsive design */
@media (max-width: 600px) {
  .w3-bar {
    flex-wrap: wrap;
  }

  .w3-bar-item.w3-button {
    margin: 4px;
    flex-grow: 1;
  }
}