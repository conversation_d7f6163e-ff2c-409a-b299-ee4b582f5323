<!DOCTYPE html>
<html>

<head>
  <!-- Essential Meta Tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <!-- SEO Meta Tags -->
  <title>LDP Binary Analyzer - Advanced AI Trading Bot for Deriv | Free Trading Tool</title>
  <meta name="description"
    content="Advanced LDP Binary Analyzer with AI-powered algorithms for Deriv trading. Free binary options bot with real-time analysis, automated strategies, and profit optimization. Start winning more trades today!">
  <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <meta name="language" content="en">
  <meta name="author" content="LDP Analyzer">
  <meta name="publisher" content="LDP Analyzer">
  <meta name="copyright" content="© 2025 LDP Analyzer">

  <!-- Canonical URL -->
  <link rel="canonical" href="https://binarybot.live/ldp/">

  <link rel="icon" href="img/favicon.png">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://binarybot.live/ldp/">
  <meta property="og:title" content="LDP Binary Analyzer - Advanced AI Trading Bot for Deriv">
  <meta property="og:description"
    content="Advanced LDP Binary Analyzer with AI-powered algorithms for Deriv trading. Free binary options bot with real-time analysis, automated strategies, and profit optimization.">
  <meta property="og:image" content="https://binarybot.live/login/base/img/deriv_binary_bot_login.png">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:image:alt" content="LDP Binary Analyzer - AI Trading Bot Interface">
  <meta property="og:site_name" content="BinaryBot.live - LDP Analyzer">
  <meta property="og:locale" content="en_US">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:url" content="https://binarybot.live/ldp/">
  <meta name="twitter:title" content="LDP Binary Analyzer - Advanced AI Trading Bot for Deriv">
  <meta name="twitter:description"
    content="Advanced LDP Binary Analyzer with AI-powered algorithms for Deriv trading. Free binary options bot with real-time analysis, automated strategies, and profit optimization.">
  <meta name="twitter:image" content="https://binarybot.live/login/base/img/deriv_binary_bot_login.png">
  <meta name="twitter:image:alt" content="LDP Binary Analyzer - AI Trading Bot Interface">>

  <!-- Mobile Theme -->
  <meta name="theme-color" content="#1a1a1a">
  <meta name="msapplication-TileColor" content="#1a1a1a">

  <!-- Performance and Security -->
  <meta name="referrer" content="strict-origin-when-cross-origin">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://cdnjs.cloudflare.com">
  <link rel="preconnect" href="https://www.googletagmanager.com">
  <link rel="dns-prefetch" href="https://www.google-analytics.com">

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "LDP Binary Analyzer",
    "description": "Advanced LDP Binary Analyzer with AI-powered algorithms for Deriv platform. Free binary options bot with real-time analysis, automated trading strategies, and profit optimization tools.",
    "url": "https://binarybot.live/ldp/",
    "applicationCategory": "FinanceApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "author": {
      "@type": "Organization",
      "name": "LDP Analyzer"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1250"
    },
    "featureList": [
      "AI-powered market analysis",
      "Real-time trading signals",
      "Automated binary options bot",
      "Multiple trading strategies",
      "Profit optimization algorithms",
      "Risk management tools",
      "Free lifetime access",
      "Live market data integration"
    ]
  }
  </script>

  <!-- CSS Resources -->
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    crossorigin="anonymous">
  <link rel="stylesheet" href="css/w3.css">
  <link rel="stylesheet" href="css/notyf.min.css">

  <!-- Google Analytics 4 -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-NB0DD2TG9Q"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-NB0DD2TG9Q', {
      page_title: 'LDP Binary Analyzer',
      page_location: window.location.href,
      content_group1: 'Trading Tools'
    });
  </script>

  <!-- Preload Critical Resources -->
  <link rel="preload" href="css/style.css" as="style">
  <link rel="preload" href="js/app.js" as="script">
</head>

<body class="EN" style="background: white;">


  <header class="header" id="header">
    <section class="wrapper container">
      <h1> LDP <span style="font-size: 0.6em;margin-left: -0.5em;text-transform: capitalize;">Analyzer</span> </h1>

      <button id="mobile-login" style="display: none" onclick="loginclick()" class="fa fa-sign-in button-3"
        role="button"> Login</button>


      <div class="burger" id="burger">
        <span class="burger-line"></span>
        <span class="burger-line"></span>
        <span class="burger-line"></span>
      </div>
      <span class="overlay"></span>
      <nav class="navbar" id="navbar">
        <ul class="menu" id="menu">



          <li class="menu-item" id="login-btn" onclick="loginclick()" style="display: none">
            <button class="fa fa-sign-in button-3" role="button"> Login</button>
          </li>

          <li class="menu-item" id="logout-btn" onclick="logiout()" style="display: none">
            <button style="background-color: #892245" class="fa fa-sign-out button-3" role="button"> Logout</button>
          </li>

          <li class="menu-item">
            <a href="/telegram" target="_blank" class="menu-link fa fa-telegram img-text-men"> Join
              Telegram </a>
          </li>
          <li class="menu-item">
            <a href="/facebook" target="_blank" class="menu-link fa fa-facebook img-text-men">
              facebook </a>
          </li>
          <li class="menu-item">
            <a href="/youtube" target="_blank" class="menu-link fa fa-youtube img-text-men"> YouTube</a>
          </li>

        </ul>
      </nav>
    </section>
  </header>


  <div class="" style="padding: 4.5em 0;;font-family: monospace;">

    <div class="row">
      <div class="column">
        <h3 class="top-lbl" id="accountdata">Account: ********</h3>
      </div>
      <div class="column">
        <h3 class="top-lbl" id="displayBal">Balance: 0.00 USD</h3>
        <input type="hidden" id="balnceinput" value="0">
      </div>
      <div class="column">
        <h3 class="top-lbl" id="displayProfitldp">Profit: 0.00 USD</h3>
      </div>
    </div>

    <div class="row" style="margin-top: 10px;">
      <div class="column">
        <h4 class="top-lbl" id="currentSymbol" style="font-size: 1.2em; color: #3f51b5;">Current Market: R_100</h4>
      </div>
    </div>

  </div>
  </div>

  <div class="menu market-menue" style="margin-top: -2.5em;" id="markt-menu">
    <span class="menu-active" title="R_100">100v</span>
    <span class="" title="R_10">10v</span>
    <span class="" title="R_25">25v</span>
    <span class="" title="R_50">50v</span>
    <span class="" title="R_75">75v</span>

    <span class="" title="1HZ10V">10(1s)</span>
    <span class="" title="1HZ25V">25(1s)</span>
    <span class="" title="1HZ50V">50(1s)</span>
    <span class="" title="1HZ75V">75(1s)</span>
    <span class="" title="1HZ100V">100(1s)</span>
  </div>


  <div id="placecol" class="placecol">
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
  </div>

  <div id="preloadingtxt">
    <img style="display: block;margin-left: auto;
    margin-right: auto;" src="img/loading.gif" alt="ldp analyser loading" />
  </div>

  <span id="market_condition"></span>
  <div class="chartContainer" id="chartContainer"></div>

  <div id="digits" class="digits">
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
  </div>

  <div id="headcol" class="headcol">
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
    <span class="_"></span>
  </div>
  <div class="chartContainerDigit" id="chartContainerDigit"></div>


  <div id="btm-tool" style="display: none;">
    <div id="opration-cosole">
      <div class="w3-bar w3-black">
        <a class="w3-bar-item w3-button button-active" id="Auto-btn" onclick="openCity('Auto')">Ai✨</a>
        <a class="w3-bar-item w3-button" id="Diff-btn" onclick="openCity('Diff')">Diff | Match</a>
        <a class="w3-bar-item w3-button" id="Over-btn" onclick="openCity('Over')">Over | Under</a>
        <a class="w3-bar-item w3-button" id="Rise-btn" onclick="openCity('Rise')">Rise | Fall</a>
        <img id="live-indicator-analyzing" class="live-indicator-analyzing" src="img/analizing.gif"
          alt="ldp analyser loading" />
        <img id="live-indicator-buy" class="live-indicator-analyzing" src="img/up.gif" alt="ldp analyser buy" />
      </div>

      <div id="Over" class="w3-container city">

        <div class="row tbl-rw">
          <div class="column" style="width: 45%;">
            <div class="row">
              <div class="column" style="width: 50%;">
                Stake:
              </div>
              <div class="column" style="width: 45%; float: right;">
                <input id="stakeinp_over" class="textbox-btm" type="number" value="1">
              </div>
            </div>
          </div>
          <div class="column" style="width: 50%;float: right;text-align: right;">
            <button class="risebtn" id="overbtn"><i class="fa fa-arrow-up"></i> Over
            </button>
          </div>
        </div>

        <div class="row tbl-rw">
          <div class="column" style="width: 45%;">
            <div class="row">
              <div class="column" style="width: 50%;">
                Prediction:
              </div>
              <div class="column" style="width: 45%; float: right;">
                <select class="textbox-btm" id="prediction_over" style="padding: 0.15em;">
                  <option value="0">0</option>
                  <option value="1">1</option>
                  <option value="2">2</option>
                  <option value="3">3</option>
                  <option value="4">4</option>
                  <option value="5">5</option>
                  <option value="6">6</option>
                  <option value="7">7</option>
                  <option value="8">8</option>
                  <option value="9">9</option>
                </select>
              </div>
            </div>
          </div>
          <div class="column" style="width: 50%;float: right;text-align: right;">
            <button class="fallbtn" id="underbtn"><i class="fa fa-arrow-down"></i> Under
            </button>
          </div>
        </div>
        <div class="row tbl-rw">
          <div class="column" style="width: 45%;">
            <div class="row">
              <div class="column" style="width: 50%;">
                Duration(t):
              </div>
              <div class="column" style="width: 45%; float: right;">
                <input id="ticksint_over" class="textbox-btm" type="number" value="5">
              </div>
            </div>
          </div>
          <div class="column" style="width: 50%;float: right;text-align: right;">

          </div>
        </div>

      </div>


      <div id="Auto" class="w3-container city" style="display:none">
        <div class="row tbl-rw">
          <div class="column" style="width: 50%;">
            <div class="row">
              <div class="column" style="width: 50%;">
                Stake:
              </div>
              <div class="column" style="width: 45%; float: right;">
                <input id="stakeinp_auto" class="textbox-btm" type="number" value="1">
              </div>
            </div>
          </div>
          <div class="column" style="width: 50%;float: right;text-align: right;">
            <button class="risebtn" id="startbtn_auto"> Start ✨
            </button>
            <button class="fallbtn" id="stopbtn_auto" style="display: none;"><i class="fa fa-stop"></i> Stop
            </button>
          </div>
        </div>

        <div class="row tbl-rw">
          <div class="column" style="width: 50%;">
            <div class="row">
              <div class="column" style="width: 50%;">
                Target Profit:
              </div>
              <div class="column" style="width: 45%; float: right;">
                <input id="tpinp_auto" class="textbox-btm" type="number" value="2">
              </div>
            </div>
          </div>
          <div class="column" style="width: 50%;float: right;text-align: right;">

          </div>
        </div>

        <div class="row tbl-rw">
          <div class="column" style="width: 50%;">
            <div class="row">
              <div class="column" style="width: 50%;">
                Stop Loss:
              </div>
              <div class="column" style="width: 45%; float: right;">
                <input id="slinp_auto" class="textbox-btm" type="number" value="50">
              </div>
            </div>
          </div>

        </div>

      </div>

      <div id="Diff" class="w3-container city" style="display:none">
        <div class="row tbl-rw">
          <div class="column" style="width: 45%;">
            <div class="row">
              <div class="column" style="width: 50%;">
                Stake:
              </div>
              <div class="column" style="width: 45%; float: right;">
                <input id="stakeinp_diff" class="textbox-btm" type="number" value="1">
              </div>
            </div>
          </div>
          <div class="column" style="width: 50%;float: right;text-align: right;">
            <button class="risebtn" id="diffbtn"><i class="fa fa-circle-o"></i> Diff
            </button>
          </div>
        </div>

        <div class="row tbl-rw">
          <div class="column" style="width: 100%;">
            <div class="row">
              <div class="column" style="width: 25%;">
                Prediction:
              </div>
              <div class="column" style="width: 20%; ">
                <select class="textbox-btm" id="prediction_diff" style="padding: 0.15em;">
                  <option value="0">0</option>
                  <option value="1">1</option>
                  <option value="2">2</option>
                  <option value="3">3</option>
                  <option value="4">4</option>
                  <option value="5">5</option>
                  <option value="6">6</option>
                  <option value="7">7</option>
                  <option value="8">8</option>
                  <option value="9">9</option>
                </select>
              </div>
              <div class="column" style="width: 50%;float: right;text-align: right;">
                <button class="fallbtn" id="matchbtn"><i class="fa fa-random"></i> Match
                </button>
              </div>
            </div>
          </div>

        </div>

      </div>

      <div id="Rise" class="w3-container city" style="display:none">

        <div class="row tbl-rw">
          <div class="column" style="width: 45%;">
            <div class="row">
              <div class="column" style="width: 50%;">
                Stake:
              </div>
              <div class="column" style="width: 45%; float: right;">
                <input id="stakeinp_rise" class="textbox-btm" type="number" value="1">
              </div>
            </div>
          </div>
          <div class="column" style="width: 50%;float: right;text-align: right;">
            <button class="risebtn" id="callbtn"><i class="fa fa-arrow-up"></i> Rise
            </button>
          </div>
        </div>
        <div class="row tbl-rw">
          <div class="column" style="width: 45%;">
            <div class="row">
              <div class="column" style="width: 50%;">
                Duration(t):
              </div>
              <div class="column" style="width: 45%; float: right;">
                <input id="ticksint_rise" class="textbox-btm" type="number" value="5">
              </div>
            </div>
          </div>
          <div class="column" style="width: 50%;float: right;text-align: right;">
            <button class="fallbtn" id="fallbtn"><i class="fa fa-arrow-down"></i> Fall
            </button>
          </div>
        </div>
      </div>



    </div>

    <div id="output" class="msgoutput">

    </div>
  </div>


  <script src="js/canvasjs.min.js"></script>
  <script src="js/notyf.min.js"></script>

  <!-- Deobfuscated JavaScript files -->
  <script src="js/app-fixed.js"></script>
  <script src="js/ai.js"></script>


  <script>
    function openCity(type) {
      var i;
      var x = document.getElementsByClassName("city");
      for (i = 0; i < x.length; i++) {
        x[i].style.display = "none";
      }

      document.getElementById("Over-btn").classList.remove("button-active");
      document.getElementById("Diff-btn").classList.remove("button-active");
      document.getElementById("Rise-btn").classList.remove("button-active");
      document.getElementById("Auto-btn").classList.remove("button-active");

      document.getElementById(type).style.display = "block";
      document.getElementById(type + "-btn").classList.add("button-active");
    }
  </script>
</body>

</html>
