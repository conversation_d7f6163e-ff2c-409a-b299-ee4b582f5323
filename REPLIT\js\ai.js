/**
 * LDP Binary Analyzer - AI Trading Algorithm
 * Deobfuscated and cleaned version of ai.min.js
 * 
 * This file contains the core AI trading logic including:
 * - Market condition analysis
 * - Technical indicators (Bollinger Bands, ATR, Volatility, Trend Strength)
 * - Signal generation for automated trading
 * - Market condition classification and notifications
 */

// Global variables for tracking market conditions and history
let previousMarketCondition = null;
const volatilityHistory = [];
const trendStrengthHistory = [];
const marketConditionHistory = [];

/**
 * Main chart update function - called on each new tick
 * Calculates technical indicators and generates trading signals
 */
function updateChart() {
    // Get reversed data points from the chart (most recent first)
    let dataPoints = chart.options.data[0].dataPoints.reverse();
    
    // Calculate technical indicators
    let bollingerBands = calculateBollingerBands(dataPoints);
    let atrData = calculateATR(dataPoints);
    let volatility = calculateVolatility(dataPoints);
    let trendStrength = calculateTrendStrength(dataPoints);
    
    // Store volatility and trend strength in history arrays
    volatilityHistory.push(volatility);
    trendStrengthHistory.push(trendStrength);
    
    // Keep only the last 50 values (0x32 = 50)
    if (volatilityHistory.length > 50) {
        volatilityHistory.shift();
        trendStrengthHistory.shift();
    }
    
    // Analyze current market conditions
    let marketCondition = checkMarketConditions(volatilityHistory, trendStrengthHistory);
    marketConditionHistory.push(marketCondition);
    
    // Keep only the last 10 market condition values (0xa = 10)
    if (marketConditionHistory.length > 10) {
        marketConditionHistory.shift();
    }
    
    // Calculate moving average of market conditions
    let marketConditionMA = calculateMarketConditionMA(marketConditionHistory);
    
    // Check if market condition has changed and notify user
    if (marketConditionMA !== previousMarketCondition) {
        console.log("Market condition changed to:", marketConditionMA);
        showNotify(getNotifyType(marketConditionMA), "Market condition: " + marketConditionMA);
        updateMarketConditionDisplay(marketConditionMA);
        previousMarketCondition = marketConditionMA;
    }
    
    // Generate trading signal based on technical analysis
    let signal = generateSignal(dataPoints, bollingerBands, atrData);
    if (signal) {
        signal.tickCount = 0;
        chart.options.data[1].dataPoints.push(signal);
    }
    
    // Clean up old signals (keep only signals from last 15 ticks)
    chart.options.data[1].dataPoints = chart.options.data[1].dataPoints.filter(signalPoint => {
        signalPoint.tickCount++;
        return signalPoint.tickCount <= 15; // 0xf = 15
    });
    
    // Re-render the chart with new data
    chart.render();
}

/**
 * Updates the market condition display on the UI
 * @param {string} condition - The market condition string
 */
function updateMarketConditionDisplay(condition) {
    const marketConditionElement = document.getElementById('market_condition');
    if (marketConditionElement) {
        marketConditionElement.textContent = condition;
        marketConditionElement.className = "market-condition " + condition.toLowerCase().replace(" ", '-');
    }
}

/**
 * Analyzes market conditions based on volatility and trend strength history
 * @param {Array} volatilityArray - Array of volatility values
 * @param {Array} trendStrengthArray - Array of trend strength values
 * @returns {number} Market condition score (-2 to 2)
 */
function checkMarketConditions(volatilityArray, trendStrengthArray) {
    // Calculate average volatility and trend strength
    let avgVolatility = volatilityArray.reduce((sum, value) => sum + value) / volatilityArray.length;
    let avgTrendStrength = trendStrengthArray.reduce((sum, value) => sum + value) / trendStrengthArray.length;
    
    // Get current (latest) values
    let currentVolatility = volatilityArray[volatilityArray.length - 1];
    let currentTrendStrength = trendStrengthArray[trendStrengthArray.length - 1];
    
    // Define thresholds for market condition analysis
    let highVolatilityThreshold = avgVolatility * 1.5;
    let lowTrendStrengthThreshold = avgTrendStrength * 0.6;
    
    // Market condition classification logic
    if (currentVolatility > highVolatilityThreshold && currentTrendStrength < lowTrendStrengthThreshold) {
        return -2; // Very bad market condition (high volatility, weak trend)
    } else if (currentVolatility > highVolatilityThreshold) {
        return -1; // Bad market condition (high volatility)
    } else if (currentTrendStrength < lowTrendStrengthThreshold) {
        return 0; // Neutral market condition (weak trend)
    } else if (currentVolatility < avgVolatility * 0.6 && currentTrendStrength > avgTrendStrength * 1.5) {
        return 2; // Very good market condition (low volatility, strong trend)
    } else {
        return currentVolatility < avgVolatility && currentTrendStrength > avgTrendStrength ? 1 : 0;
        // Good market condition if below avg volatility and above avg trend strength, otherwise neutral
    }
}

/**
 * Calculates moving average of market conditions and converts to descriptive text
 * @param {Array} conditionHistory - Array of market condition scores
 * @returns {string} Market condition description
 */
function calculateMarketConditionMA(conditionHistory) {
    let sum = conditionHistory.reduce((total, condition) => total + condition, 0);
    let average = sum / conditionHistory.length;
    
    // Convert numerical average to descriptive text
    if (average <= -1.5) {
        return "Very Bad";
    }
    if (average <= -0.5) {
        return 'Bad';
    }
    if (average >= 1.5) {
        return "Very Good";
    }
    if (average >= 0.5) {
        return "Good";
    }
    return 'Neutral';
}

/**
 * Gets notification type based on market condition for UI alerts
 * @param {string} condition - Market condition string
 * @returns {string} Notification type (error, info, success)
 */
function getNotifyType(condition) {
    switch (condition) {
        case "Very Bad":
        case "Bad":
            return "error";
        case "Neutral":
            return "info";
        case "Good":
        case "Very Good":
            return "success";
        default:
            return "info";
    }
}

/**
 * Calculates price volatility using standard deviation of returns
 * @param {Array} dataPoints - Array of price data points with x,y values
 * @param {number} period - Period for calculation (default: 14)
 * @returns {number|null} Annualized volatility or null if insufficient data
 */
function calculateVolatility(dataPoints, period = 14) {
    if (dataPoints.length < period) {
        return null;
    }

    // Calculate price returns (percentage change between consecutive prices)
    let returns = [];
    for (let i = 1; i < dataPoints.length; i++) {
        returns.push((dataPoints[i].y - dataPoints[i - 1].y) / dataPoints[i - 1].y);
    }

    // Calculate mean return
    let meanReturn = returns.reduce((sum, returnValue) => sum + returnValue, 0) / returns.length;

    // Calculate squared deviations from mean
    let squaredDeviations = returns.map(returnValue => Math.pow(returnValue - meanReturn, 2));

    // Calculate variance (average of squared deviations)
    let variance = squaredDeviations.reduce((sum, deviation) => sum + deviation, 0) / squaredDeviations.length;

    // Return annualized volatility (standard deviation * sqrt(252 trading days))
    return Math.sqrt(variance) * Math.sqrt(252); // 0xfc = 252
}

/**
 * Calculates trend strength using price momentum and moving average
 * @param {Array} dataPoints - Array of price data points
 * @param {number} period - Period for calculation (default: 14)
 * @returns {number|null} Trend strength value or null if insufficient data
 */
function calculateTrendStrength(dataPoints, period = 14) {
    if (dataPoints.length < period) {
        return null;
    }

    // Calculate Simple Moving Average
    let smaData = calculateSMA(dataPoints, period);
    let currentSMA = smaData[smaData.length - 1].y;

    // Calculate price momentum over the period
    let priceChange = (dataPoints[dataPoints.length - 1].y - dataPoints[dataPoints.length - period].y) /
                     dataPoints[dataPoints.length - period].y;

    // Return trend strength (momentum adjusted by current price relative to SMA)
    return Math.abs(priceChange) * (dataPoints[dataPoints.length - 1].y / currentSMA);
}

/**
 * Generates buy/sell signals based on Bollinger Bands and ATR
 * @param {Array} priceData - Array of price data points
 * @param {Array} bollingerBands - Bollinger Bands data
 * @param {Array} atrData - Average True Range data
 * @returns {Object|null} Signal object or null if no signal
 */
function generateSignal(priceData, bollingerBands, atrData) {
    // Check if we have enough data for signal generation
    if (priceData.length < 2 || bollingerBands.length < 2 || atrData.length < 2) {
        console.log("Not enough data for signal generation");
        return null;
    }

    let currentIndex = priceData.length - 1;
    let currentBB = bollingerBands[bollingerBands.length - 1];
    let previousBB = bollingerBands[bollingerBands.length - 2];
    let currentATR = atrData[atrData.length - 1].y;

    // Buy signal: Price crosses above lower Bollinger Band with ATR confirmation
    if (priceData[currentIndex].y > currentBB.lower &&
        priceData[currentIndex - 1].y <= previousBB.lower &&
        priceData[currentIndex].y - currentBB.lower < currentATR) {

        console.log("Buy signal generated");

        // Execute automated trade if auto trading is enabled
        if (AUTO_TRADING_START && IS_CALL_TRADE) {
            IS_CALL_TRADE = false;
            AutoCaLL();
        }

        return {
            'x': priceData[currentIndex].x,
            'y': priceData[currentIndex].y,
            'markerColor': "#32CD32", // Green
            'markerType': "none",
            'markerSize': 12, // 0xc = 12
            'indexLabel': '▲',
            'indexLabelFontColor': "#32CD32",
            'tickCount': 0,
            'indexLabelFontSize': 35 // 0x23 = 35
        };
    }
    // Sell signal: Price crosses below upper Bollinger Band with ATR confirmation
    else if (priceData[currentIndex].y < currentBB.upper &&
             priceData[currentIndex - 1].y >= previousBB.upper &&
             currentBB.upper - priceData[currentIndex].y < currentATR) {

        console.log("Sell signal generated");

        // Execute automated trade if auto trading is enabled
        if (AUTO_TRADING_START && IS_CALL_TRADE) {
            IS_CALL_TRADE = false;
            AutoPuTT();
        }

        return {
            'x': priceData[currentIndex].x,
            'y': priceData[currentIndex].y,
            'markerColor': "#FF0000", // Red
            'markerType': "none",
            'markerSize': 12, // 0xc = 12
            'indexLabel': '▼',
            'indexLabelFontColor': "#FF0000",
            'tickCount': 0,
            'indexLabelFontSize': 35 // 0x23 = 35
        };
    }

    return null;
}

/**
 * Calculates Bollinger Bands (moving average with standard deviation bands)
 * @param {Array} dataPoints - Array of price data points
 * @param {number} period - Period for moving average (default: 20)
 * @param {number} stdDev - Standard deviation multiplier (default: 2)
 * @returns {Array} Array of Bollinger Band data with upper, middle, lower values
 */
function calculateBollingerBands(dataPoints, period = 20, stdDev = 2) {
    let smaData = calculateSMA(dataPoints, period);
    let bollingerBands = [];

    // Calculate Bollinger Bands for each point where we have enough data
    for (let i = period - 1; i < dataPoints.length; i++) {
        let sum = 0;

        // Calculate standard deviation for the current period
        for (let j = 0; j < period; j++) {
            sum += Math.pow(dataPoints[i - j].y - smaData[i - period + 1].y, 2);
        }

        let standardDeviation = Math.sqrt(sum / period);

        bollingerBands.push({
            'x': dataPoints[i].x,
            'middle': smaData[i - period + 1].y,
            'upper': smaData[i - period + 1].y + stdDev * standardDeviation,
            'lower': smaData[i - period + 1].y - stdDev * standardDeviation
        });
    }

    return bollingerBands;
}

/**
 * Calculates Average True Range (ATR) - measure of volatility
 * @param {Array} dataPoints - Array of price data points
 * @param {number} period - Period for ATR calculation (default: 14)
 * @returns {Array} Array of ATR values
 */
function calculateATR(dataPoints, period = 14) {
    let trueRanges = [];
    let atrData = [];

    // Calculate True Range for each period
    for (let i = 1; i < dataPoints.length; i++) {
        let high = dataPoints[i].y;
        let low = dataPoints[i].y;
        let previousClose = dataPoints[i - 1].y;

        // True Range is the maximum of:
        // 1. High - Low
        // 2. |High - Previous Close|
        // 3. |Low - Previous Close|
        let highLow = high - low;
        let highPrevClose = Math.abs(high - previousClose);
        let lowPrevClose = Math.abs(low - previousClose);

        trueRanges.push(Math.max(highLow, highPrevClose, lowPrevClose));
    }

    // Calculate initial ATR (simple average of first 'period' true ranges)
    let initialATR = trueRanges.slice(0, period).reduce((sum, tr) => sum + tr) / period;
    atrData.push({
        'x': dataPoints[period].x,
        'y': initialATR
    });

    // Calculate subsequent ATR values using smoothed average
    for (let i = period + 1; i < dataPoints.length; i++) {
        let smoothedATR = (atrData[atrData.length - 1].y * (period - 1) + trueRanges[i - 1]) / period;
        atrData.push({
            'x': dataPoints[i].x,
            'y': smoothedATR
        });
    }

    return atrData;
}

/**
 * Calculates Simple Moving Average (SMA)
 * @param {Array} dataPoints - Array of price data points with x,y values
 * @param {number} period - Period for moving average
 * @returns {Array} Array of SMA data points
 */
function calculateSMA(dataPoints, period) {
    let smaData = [];

    if (dataPoints.length < period) {
        return smaData;
    }

    // Calculate SMA for each point where we have enough data
    for (let i = period - 1; i < dataPoints.length; i++) {
        let sum = 0;

        // Sum the last 'period' values
        for (let j = 0; j < period; j++) {
            sum += dataPoints[i - j].y;
        }

        smaData.push({
            'x': dataPoints[i].x,
            'y': sum / period
        });
    }

    return smaData;
}
