/**
 * LDP Binary Analyzer - Working Version
 * Based on original app.ldp.min.js with improved readability
 */

// WebSocket and data variables (from original)
var websocketUrls = ['wss://ws.derivws.com/websockets/v3?app_id=21317'];
var websocket;
var responseData;
var randomIndex = "R_100";
var subscriptionId;
var symbolNames = ["R_100", "R_10", "R_25", 'R_50', "R_75", "1HZ10V", "1HZ25V", "1HZ50V", '1HZ75V', "1HZ100V"];

// Chart and data arrays (matching original variable names)
var chartDataPoints = []; // original: dps
var digitChartDataPoints = []; // original: dps2
var timestamps = [0]; // original: time
var spotPrices = [0]; // original: spot
var digitValues = [0]; // original: digit
var tickValues = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]; // original: tic
var thickValues = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]; // original: thick
var decimalPlaces = 2; // original: xd
var chart, chart2;
var xValue, yValue, markerType, markerColor;

// Trading variables
var currency = "USD";
var isLoading = true; // original: isloading
var startingBalance = 0; // original: starting_balance
var lastContractId = ''; // original: last_contract_id
var AUTO_TRADING_START = false;
var IS_CALL_TRADE = true;
var lastTradeWon = true; // original: last_trade
var IS_OPEN = false;

// Notification system
const notyf = new Notyf({
    'duration': 2500,
    'position': { 'x': 'left', 'y': "top" },
    'types': [
        { 'type': "error", 'background': 'indianred', 'dismissible': true },
        { 'type': "warning", 'background': "#ff9800", 'dismissible': true },
        { 'type': "info", 'background': "#29abe2", 'dismissible': true },
        { 'type': "success", 'background': "#3dc663", 'dismissible': true }
    ]
});

function showNotify(type, message) {
    notyf.open({ 'type': type, 'message': message });
}

// Main window load event (following original pattern exactly)
window.addEventListener('load', function() {
    var rndMenu = document.querySelectorAll("#markt-menu > span");
    
    // Set up menu click listeners (original pattern)
    for (var i = 0; i < rndMenu.length; i++) {
        addMenuClickListener(rndMenu[i]);
    }
    
    function addMenuClickListener(menuElement) {
        menuElement.addEventListener("click", function() {
            selectMenuElement(menuElement);
        });
    }
    
    // Digit animation function (original: _0x3d7f30)
    function updateDigitAnimation(position, direction) {
        var currentClass = document.querySelector("#digits > span:nth-child(" + position + ')').className;
        if (currentClass != 'digits_moved_' + direction) {
            document.querySelector("#digits > span:nth-child(" + position + ')').classList.remove(currentClass);
            document.querySelector("#digits > span:nth-child(" + position + ')').classList.add("digits_moved_" + direction);
        }
    }
    
    // Header animation function (original: _0x2d5059)
    function updateHeaderAnimation(position, direction) {
        var currentClass = document.querySelector("#headcol > span:nth-child(" + position + ')').className;
        if (currentClass != "Head_moved_" + direction) {
            document.querySelector("#headcol > span:nth-child(" + position + ')').classList.remove(currentClass);
            document.querySelector("#headcol > span:nth-child(" + position + ')').classList.add("Head_moved_" + direction);
        }
    }
    
    // Symbol configuration function (original: _0xa11c87)
    function getCurrentSymbolConfig() {
        var activeElement = document.querySelector("#markt-menu > span.menu-active");
        if (!activeElement) return;
        
        var selectedSymbol = activeElement.title;
        switch (selectedSymbol) {
            case symbolNames[0]: randomIndex = "R_100"; decimalPlaces = 2; break;
            case symbolNames[1]: randomIndex = "R_10"; decimalPlaces = 3; break;
            case symbolNames[2]: randomIndex = "R_25"; decimalPlaces = 3; break;
            case symbolNames[3]: randomIndex = "R_50"; decimalPlaces = 4; break;
            case symbolNames[4]: randomIndex = "R_75"; decimalPlaces = 4; break;
            case symbolNames[5]: randomIndex = "1HZ10V"; decimalPlaces = 2; break;
            case symbolNames[6]: randomIndex = "1HZ25V"; decimalPlaces = 2; break;
            case symbolNames[7]: randomIndex = "1HZ50V"; decimalPlaces = 2; break;
            case symbolNames[8]: randomIndex = "1HZ75V"; decimalPlaces = 2; break;
            case symbolNames[9]: randomIndex = "1HZ100V"; decimalPlaces = 2; break;
            default: randomIndex = 'R_100'; decimalPlaces = 2; break;
        }
    }
    
    // Menu selection function (original: _0x5e5b48 - missing from minified)
    function selectMenuElement(selectedElement) {
        // Remove active class from all menu elements
        var allMenuElements = document.querySelectorAll("#markt-menu > span");
        for (var i = 0; i < allMenuElements.length; i++) {
            allMenuElements[i].classList.remove("menu-active");
        }
        
        // Add active class to selected element
        selectedElement.classList.add("menu-active");
        
        // Update symbol configuration
        getCurrentSymbolConfig();
        console.log("Symbol changed to:", randomIndex);
        
        // Update current symbol display
        var symbolDisplay = document.getElementById("currentSymbol");
        if (symbolDisplay) {
            symbolDisplay.textContent = "Current Market: " + randomIndex;
        }
        
        // Switch WebSocket subscription if connected
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            // Clear existing data
            chartDataPoints = [];
            digitChartDataPoints = [];
            
            // Unsubscribe and resubscribe
            websocket.send(JSON.stringify({ 'forget': subscriptionId }));
            websocket.send(JSON.stringify({ 'forget_all': 'ticks' }));
            websocket.send(JSON.stringify({ 'ticks': randomIndex }));
            addLogMessage("<span style=\"color: #3f51b5;\">Connecting to market (" + randomIndex + ")</span>");
        }
    }
    
    // Initialize symbol configuration
    getCurrentSymbolConfig();
    
    // Initialize WebSocket (original pattern)
    websocket = new WebSocket(websocketUrls[0]);
    var output = document.getElementById("output");
    var timestampPrefix = '';
    
    // Authorization function (original: _0x13501e)
    function authorizeConnection(token) {
        timestampPrefix = " " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ";
        addLogMessage(timestampPrefix + "Try to Connecting...");
        websocket.send(JSON.stringify({ 'authorize': token }));
    }
    
    // WebSocket connection opened (original pattern)
    websocket.onopen = function(event) {
        console.log("WebSocket connected");
        websocket.send(JSON.stringify({ 'ticks': randomIndex }));
    };
    
    // Log message function (original: _0x4b68b5)
    function addLogMessage(message, borderType) {
        var messageDiv = document.createElement("div");
        messageDiv.style.wordWrap = "break-word";
        messageDiv.style.fontSize = "97%";
        if (borderType == 't') messageDiv.style.borderTop = "thin dotted #000000";
        if (borderType == 'b') messageDiv.style.borderBottom = "thin dotted #000000";
        messageDiv.innerHTML = message;
        output.appendChild(messageDiv);
        output.insertBefore(messageDiv, output.childNodes[0]);
    }

    // WebSocket message handler (following original pattern exactly)
    websocket.onmessage = function(messageEvent) {
        responseData = JSON.parse(messageEvent.data);

        // Handle tick data (original pattern)
        if (responseData.tick) {
            getCurrentSymbolConfig();
            if (responseData.echo_req.ticks == randomIndex) {
                subscriptionId = responseData.tick.id;
                websocket.send(JSON.stringify({
                    'ticks_history': randomIndex,
                    'end': "latest",
                    'start': 1,
                    'style': "ticks",
                    'count': 21
                }));
            } else {
                websocket.send(JSON.stringify({ 'forget': subscriptionId }));
                websocket.send(JSON.stringify({ 'forget_all': 'ticks' }));
                websocket.send(JSON.stringify({ 'ticks': randomIndex }));
                addLogMessage("<span style=\"color: #3f51b5;\">" + timestampPrefix + "Connecting to market (" + randomIndex + ")</span>");
            }
        }

        // Handle historical data (original pattern)
        if (responseData.history) {
            if (responseData.echo_req.ticks_history == randomIndex) {
                // Clean up chart watermarks (original pattern)
                if (document.querySelector("#chartContainer > div > a")) {
                    if (document.querySelector("#chartContainer > div > a").innerText != "            ") {
                        document.querySelector("#chartContainer > div > a").innerHTML = "            ";
                        document.querySelector("#chartContainer > div > a").href = "https://www.binarybot.live";
                    }
                }
                if (document.querySelector("#chartContainerDigit > div > a")) {
                    if (document.querySelector("#chartContainerDigit > div > a").innerText != "        ") {
                        document.querySelector("#chartContainerDigit > div > a").innerHTML = "          ";
                        document.querySelector("#chartContainerDigit > div > a").href = 'https://www.binarybot.live';
                    }
                }

                // Process historical data (original pattern: for (_0x10b03f = 0x0; _0x10b03f < 21; _0x10b03f++))
                for (var dataIndex = 0; dataIndex < 21; dataIndex++) {
                    timestamps[dataIndex] = responseData.history.times[20 - dataIndex]; // 0x14 = 20
                    spotPrices[dataIndex] = responseData.history.prices[20 - dataIndex];
                    spotPrices[dataIndex] = Number(spotPrices[dataIndex]).toFixed(decimalPlaces);
                    digitValues[dataIndex] = spotPrices[dataIndex].slice(-1);
                }

                // Prepare chart data points (original pattern)
                for (var dataIndex = 0; dataIndex < 21; dataIndex++) {
                    xValue = new Date(timestamps[dataIndex] * 1000); // 0x3e8 = 1000
                    yValue = parseFloat(spotPrices[dataIndex]);
                    if (dataIndex == 0) {
                        markerType = 'circle';
                    } else {
                        markerType = 'none';
                    }

                    if (yValue == Math.max.apply(null, spotPrices)) {
                        markerColor = "#29abe2";
                        markerType = "circle";
                    } else if (yValue == Math.min.apply(null, spotPrices)) {
                        markerColor = "#c03";
                        markerType = "circle";
                    } else {
                        markerColor = "#32cd32";
                    }

                    chartDataPoints.push({
                        'x': xValue,
                        'y': yValue,
                        'markerType': markerType,
                        'markerColor': markerColor,
                        'markerBorderColor': "#ccc"
                    });
                }

                // Keep only the last 21 data points (original pattern)
                if (chartDataPoints.length > 21) {
                    while (chartDataPoints.length != 21) {
                        chartDataPoints.shift();
                    }
                }

                chart.render();
                spotPrices.reverse();
                digitValues.reverse();
                updateChart(); // Call AI analysis function

                // Process digits and update UI (original pattern: for (_0x10b03f = 0x1; _0x10b03f < 21; _0x10b03f++))
                for (var digitIndex = 1; digitIndex < 21; digitIndex++) {
                    document.querySelector("#digits > span:nth-child(" + digitIndex + ')').innerHTML = digitValues[digitIndex];
                    var currentPrice = parseFloat(spotPrices[20]); // 0x14 = 20

                    // Determine price movement direction for header styling
                    if (currentPrice == Math.max.apply(null, spotPrices)) {
                        var priceDirection = 'up';
                        var headerColor = "#29abe2";
                    } else if (currentPrice == Math.min.apply(null, spotPrices)) {
                        var priceDirection = "down";
                        headerColor = '#c03';
                    } else {
                        var priceDirection = 'mid';
                        headerColor = "#32cd32";
                    }

                    // Calculate digit movement and update animations (original pattern)
                    if (spotPrices[digitIndex - 1] < spotPrices[digitIndex]) {
                        updateDigitAnimation(digitIndex, 'up');
                        if (digitValues[digitIndex] != 0) {
                            var digitMovement = digitValues[digitIndex] * 1;
                        }
                        if (digitValues[digitIndex - 1] > 5 && digitValues[digitIndex] == 0) {
                            var digitMovement = 10; // 0xa = 10
                        }
                        if (digitValues[digitIndex - 1] <= 5 && digitValues[digitIndex] == 0) {
                            var digitMovement = 0;
                        }
                    } else if (spotPrices[digitIndex - 1] > spotPrices[digitIndex]) {
                        updateDigitAnimation(digitIndex, 'down');
                        if (digitValues[digitIndex] != 0) {
                            var digitMovement = digitValues[digitIndex] * -1;
                        }
                        if (digitValues[digitIndex - 1] > 5 && digitValues[digitIndex] == 0) {
                            var digitMovement = -10; // -0xa = -10
                        }
                        if (digitValues[digitIndex - 1] <= 5 && digitValues[digitIndex] == 0) {
                            var digitMovement = 0; // -0x0 = 0
                        }
                    } else if (spotPrices[digitIndex - 1] == spotPrices[digitIndex] && digitIndex - 1 > 0) {
                        // Handle equal prices by copying previous movement
                        if (document.querySelector("#digits > span:nth-child(" + (digitIndex - 1) + ')').className == "digits_moved_up") {
                            updateDigitAnimation(digitIndex, 'up');
                        } else if (document.querySelector("#digits > span:nth-child(" + (digitIndex - 1) + ')').className == "digits_moved_down") {
                            updateDigitAnimation(digitIndex, "down");
                        }
                    }

                    tickValues.shift(0);
                    tickValues.push(digitMovement);
                }

                var chartColor = "#29abe2";
                thickValues.shift(0);
                thickValues.push(priceDirection);

                // Update digit chart data (original pattern)
                for (var digitIndex = 1; digitIndex < 21; digitIndex++) {
                    if (spotPrices[digitIndex - 1] < spotPrices[digitIndex]) {
                        updateDigitAnimation(digitIndex, 'up');
                        chartColor = '#29abe2';
                    } else if (spotPrices[digitIndex - 1] > spotPrices[digitIndex]) {
                        updateDigitAnimation(digitIndex, 'down');
                        chartColor = "#c03";
                    }
                    updateHeaderAnimation(digitIndex, thickValues[digitIndex - 1]);
                    document.querySelector("#headcol > span:nth-child(" + digitIndex + ')').innerHTML = tickValues;
                    var xDigit = digitIndex;
                    var yDigit = parseFloat(tickValues[digitIndex - 1]);

                    digitChartDataPoints.push({
                        'x': xDigit,
                        'y': yDigit,
                        'indexLabel': digitValues[digitIndex],
                        'indexLabelFontWeight': "bold",
                        'indexLabelFontSize': 11, // 0xb = 11
                        'markerType': "circle",
                        'markerColor': chartColor,
                        'markerBorderColor': "#ccc"
                    });
                }

                // Keep only the last 20 digit chart points (original pattern)
                if (digitChartDataPoints.length > 21) {
                    while (digitChartDataPoints.length != 20) { // 0x14 = 20
                        digitChartDataPoints.shift();
                    }
                }

                chart2.render();

                // Handle initial loading completion (original pattern)
                if (isLoading) {
                    document.getElementById("preloadingtxt").style.display = "none";
                    isLoading = false;
                    var apiToken = getCookie("api_token") || 'lX8QAY83KmTSa4P';
                    if (apiToken) {
                        setTimeout(function() {
                            authorizeConnection(apiToken);
                        }, 1000); // 0x3e8 = 1000ms
                    } else {
                        addLogMessage("<span style=\"color: #000000;\">" + timestampPrefix + "Please log in to your Deriv account to start trading..</span>", 't');
                        document.getElementById("logout-btn").style.display = "none";
                        document.getElementById("login-btn").style.display = "block";
                        document.getElementById("mobile-login").style.display = "block";
                        document.getElementById("btm-tool").style.display = 'block';
                        document.getElementById("startbtn_auto").classList.add('disabledbtn');
                        openCity("Auto");
                    }
                }
            }
        }

        // Handle different message types (original pattern)
        var messageType = responseData.msg_type;

        // Handle errors (original pattern)
        if (responseData.error && responseData.error.code != 'AlreadySubscribed') {
            addLogMessage("<span style=\"color: red;\"> " + (" " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ") + '' + responseData.error.message + "</span>", 'b');
            if (AUTO_TRADING_START) {
                stopAutoTrading();
            }
        }

        // Handle authorization response (original pattern)
        if (messageType == "authorize") {
            if (responseData.error) {
                addLogMessage("<span style=\"color: #ffffff;\">" + timestampPrefix + "Please log in to your Deriv account to start trading..</span>", 't');
                document.getElementById("login-btn").style.display = 'block';
                document.getElementById("mobile-login").style.display = 'block';
                document.getElementById("btm-tool").style.display = "block";
                openCity("Auto");
            } else {
                var balance = responseData.authorize.balance;
                var email = responseData.authorize.email;
                currency = responseData.authorize.currency;
                sessionStorage.balanceStarting = balance;
                startingBalance = balance;
                addLogMessage("<span style=\"color: #4caf50;\">" + timestampPrefix + "Connected To The Deriv Market " + randomIndex + "</span>", 't');
                chart.options.data[1].dataPoints = [];
                var fullName = responseData.authorize.fullname;
                var isVirtual = responseData.authorize.is_virtual;
                var loginId = responseData.authorize.loginid;
                sessionStorage.fullnameSaved = fullName;
                sessionStorage.cr_accountSaved = loginId;
                sessionStorage.virtual_accSaved = isVirtual;
                sessionStorage.emailSaved = email;
                sessionStorage.accountType = isVirtual == '0' ? "RealAccount" : 'VirtualAccount';
                sessionStorage.balanceNow = Number(balance);
                document.getElementById("displayBal").innerHTML = "Balance: " + sessionStorage.balanceNow + " " + currency;
                document.getElementById("accountdata").innerHTML = "Account: " + sessionStorage.cr_accountSaved;
                document.getElementById("logout-btn").style.display = "block";
                document.getElementById("btm-tool").style.display = "block";
                document.getElementById("login-btn").style.display = "none";
                document.getElementById("mobile-login").style.display = 'none';
                openCity("Auto");
                subscribeToBalance();
            }
        }
        // Handle balance updates (original pattern)
        else if (messageType == "balance") {
            var newBalance = responseData.balance?.["balance"];
            var previousBalance = sessionStorage.balanceNow;
            sessionStorage.balanceNow = Number(newBalance);
            var balanceChange = newBalance - previousBalance;
            var profit = newBalance - startingBalance;

            if (balanceChange > 0) {
                addLogMessage("<span style=\"color: #4CAF50;\"> " + timestampPrefix + '' + lastContractId + " PROFIT (" + Math.abs(balanceChange).toFixed(2) + ") " + currency + "</span>");
                lastTradeWon = true;
            } else if (balanceChange < 0) {
                lastTradeWon = false;
                addLogMessage("<span style=\"color: #f20202;\"> " + timestampPrefix + '' + lastContractId + " LOSS (" + Math.abs(balanceChange).toFixed(2) + ") " + currency + "</span>");
            }
            updateBalanceDisplay(newBalance);
        }
        // Handle buy confirmation (original pattern)
        else if (messageType == "buy") {
            IS_OPEN = true;
            setTradingIndicator(1);
            var buyResponse = responseData.buy;
            lastContractId = buyResponse.contract_id;
            addLogMessage("<span style=\"color: #122212;\"> " + timestampPrefix + '' + lastContractId + ": (" + buyResponse.buy_price + ") " + buyResponse.longcode + "</span>");
        }
    };

    // Utility functions
    function updateBalanceDisplay(newBalance) {
        IS_OPEN = false;
        document.getElementById("displayBal").innerHTML = "Balance: " + newBalance + " " + currency;
        var profit = newBalance - startingBalance;
        var color = profit > 0 ? '#4CAF50' : profit == 0 ? "black" : "#f20202";
        document.getElementById('displayProfitldp').innerHTML = "<span style=\"color: " + color + ";\"> Profit: " + profit.toFixed(2) + " " + currency + "</span>";

        if (AUTO_TRADING_START) {
            IS_CALL_TRADE = true;
            if (profit >= sessionStorage.targetProft) {
                AUTO_TRADING_START = false;
                stopAutoTrading();
            } else if (Math.abs(profit) >= sessionStorage.stopLoss) {
                AUTO_TRADING_START = false;
                stopAutoTrading();
            }
        }
    }

    function setTradingIndicator(mode) {
        if (mode == 0) {
            document.getElementById("live-indicator-analyzing").style.display = "inline-block";
            document.getElementById("live-indicator-buy").style.display = "none";
        } else if (mode == 1) {
            document.getElementById("live-indicator-analyzing").style.display = 'none';
            document.getElementById('live-indicator-buy').style.display = "inline-block";
        } else {
            document.getElementById('live-indicator-analyzing').style.display = "none";
            document.getElementById("live-indicator-buy").style.display = "none";
        }
    }

    function stopAutoTrading() {
        setTradingIndicator(3);
        addLogMessage("<span style=\"color: #625959;\"> " + timestampPrefix + "Trading Stopped. </span>", 'b');
        document.getElementById('stakeinp_auto').value = 1;
    }

    function subscribeToBalance() {
        websocket.send(JSON.stringify({ 'balance': 1, 'subscribe': 1 }));
    }

    function getCookie(name) {
        var match = document.cookie.match("(^|;) ?" + name + "=([^;]*)(;|$)");
        return match ? match[2] : null;
    }

    // Initialize charts (original pattern)
    chart = new CanvasJS.Chart('chartContainer', {
        'animationEnabled': false,
        'theme': "light2",
        'title': { 'titleFontSize': 0, 'text': '' },
        'toolTip': {
            'enabled': true,
            'animationEnabled': true,
            'borderColor': "#ccc",
            'borderThickness': 1,
            'fontColor': '#000',
            'content': "{name}: {y}"
        },
        'axisX': {
            'includeZero': false,
            'titleFontSize': 0,
            'labelFontSize': 0,
            'gridThickness': 0,
            'tickLength': 0,
            'lineThickness': 1
        },
        'axisY': {
            'includeZero': false,
            'labelFontSize': 10,
            'gridThickness': 0,
            'lineThickness': 1
        },
        'legend': {
            'cursor': "pointer",
            'verticalAlign': "top",
            'horizontalAlign': 'left',
            'dockInsidePlotArea': true
        },
        'data': [{
            'type': "line",
            'name': "Price",
            'showInLegend': true,
            'lineColor': "#67636a",
            'lineThickness': 1,
            'markerType': 'none',
            'dataPoints': chartDataPoints
        }, {
            'type': "scatter",
            'name': "Signals",
            'showInLegend': false,
            'indexLabelTextAlign': 'left',
            'markerSize': 12,
            'dataPoints': []
        }]
    });

    chart2 = new CanvasJS.Chart("chartContainerDigit", {
        'animationEnabled': false,
        'theme': "light2",
        'title': { 'titleFontSize': 0, 'text': '' },
        'toolTip': {
            'enabled': true,
            'animationEnabled': true,
            'borderColor': "#ccc",
            'borderThickness': 1,
            'fontColor': "#000",
            'content': "{y}"
        },
        'axisX': {
            'includeZero': false,
            'titleFontSize': 0,
            'labelFontSize': 0,
            'gridThickness': 0,
            'tickLength': 0,
            'lineThickness': 1
        },
        'axisY': {
            'stripLines': [{
                'startValue': -0.05,
                'endValue': 0.05,
                'color': 'black'
            }],
            'valueFormatString': "#000",
            'includeZero': false,
            'titleFontSize': 0,
            'labelFontSize': 0,
            'gridThickness': 0,
            'tickLength': 0,
            'lineThickness': 1
        },
        'data': [{
            'type': "line",
            'lineColor': '#ccc',
            'lineThickness': 1,
            'markerType': "none",
            'markerSize': 6,
            'markerBorderThickness': 0,
            'dataPoints': digitChartDataPoints
        }]
    });

}, false); // End of window load event listener

// Auto trading functions (called by AI)
function AutoPuTT() {
    var stakeAmount = Number(document.getElementById("stakeinp_auto").value);
    var formattedStake = stakeAmount.toFixed(2);
    websocket.send(JSON.stringify({
        'buy': '1', 'price': formattedStake,
        'parameters': {
            'amount': formattedStake, 'basis': 'stake', 'contract_type': "PUT",
            'currency': currency, 'duration': 5, 'duration_unit': 't', 'symbol': randomIndex
        }
    }));
}

function AutoCaLL() {
    var stakeAmount = Number(document.getElementById('stakeinp_auto').value);
    var formattedStake = stakeAmount.toFixed(2);
    websocket.send(JSON.stringify({
        'buy': '1', 'price': formattedStake,
        'parameters': {
            'amount': formattedStake, 'basis': "stake", 'contract_type': 'CALL',
            'currency': currency, 'duration': 5, 'duration_unit': 't', 'symbol': randomIndex
        }
    }));
}

// Called by AI analysis to update chart with signals
function updateChart() {
    // This function is called by the AI analysis
    // The AI will add signal markers to chart.options.data[1].dataPoints
    console.log("updateChart called - AI analysis can add signals here");
}

// Opens a specific tab in the trading interface
function openCity(tabName) {
    var tabcontent = document.getElementsByClassName("tabcontent");
    for (var i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }
    var tablinks = document.getElementsByClassName("tablinks");
    for (var i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }
    var targetTab = document.getElementById(tabName);
    if (targetTab) {
        targetTab.style.display = "block";
    }
}

// Utility functions
function loginclick() { window.location.href = '../login'; }
function logiout() { setCookie("api_token", '', 30); window.location.href = "/ldp"; }

function setCookie(name, value, days) {
    var expires = '';
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
        expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + '=' + (value || '') + expires + "; path=/";
}
