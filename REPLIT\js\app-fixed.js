/**
 * LDP Binary Analyzer - Fixed Main Application Logic
 * Clean, working version based on successful WebSocket test
 */

// WebSocket configuration
var websocketUrls = ['wss://ws.derivws.com/websockets/v3?app_id=21317'];
var websocket;
var responseData;
var randomIndex = "R_100";
var subscriptionId;
var symbolNames = ["R_100", "R_10", "R_25", 'R_50', "R_75", "1HZ10V", "1HZ25V", "1HZ50V", '1HZ75V', "1HZ100V"];

// Chart and data variables
var chartDataPoints = [];
var digitChartDataPoints = [];
var timestamps = [0];
var spotPrices = [0];
var digitValues = [0];
var tickValues = [];
var thickValues = [];
var decimalPlaces = 2;
var chart, chart2;
var xValue, yValue, markerType, markerColor;

// Trading variables
var currency = "USD";
var isLoading = true;
var startingBalance = 0;
var lastContractId = '';
var AUTO_TRADING_START = false;
var IS_CALL_TRADE = true;
var lastTradeWon = true;
var IS_OPEN = false;

// Notification system
const notyf = new Notyf({
    'duration': 2500,
    'position': { 'x': 'left', 'y': "top" },
    'types': [
        { 'type': "error", 'background': 'indianred', 'dismissible': true },
        { 'type': "warning", 'background': "#ff9800", 'dismissible': true },
        { 'type': "info", 'background': "#29abe2", 'dismissible': true },
        { 'type': "success", 'background': "#3dc663", 'dismissible': true }
    ]
});

function showNotify(type, message) {
    notyf.open({ 'type': type, 'message': message });
}

/**
 * Gets the currently selected trading symbol and sets global variables (based on original _0xa11c87)
 */
function getCurrentSymbolConfig() {
    var activeElement = document.querySelector("#markt-menu > span.menu-active");
    if (!activeElement) return;

    var randomValue = activeElement.title;
    switch (randomValue) {
        case symbolNames[0]: // "R_100"
            randomIndex = "R_100";
            decimalPlaces = 2;
            break;
        case symbolNames[1]: // "R_10"
            randomIndex = "R_10";
            decimalPlaces = 3;
            break;
        case symbolNames[2]: // "R_25"
            randomIndex = "R_25";
            decimalPlaces = 3;
            break;
        case symbolNames[3]: // "R_50"
            randomIndex = "R_50";
            decimalPlaces = 4;
            break;
        case symbolNames[4]: // "R_75"
            randomIndex = "R_75";
            decimalPlaces = 4;
            break;
        case symbolNames[5]: // "1HZ10V"
            randomIndex = "1HZ10V";
            decimalPlaces = 2;
            break;
        case symbolNames[6]: // "1HZ25V"
            randomIndex = "1HZ25V";
            decimalPlaces = 2;
            break;
        case symbolNames[7]: // "1HZ50V"
            randomIndex = "1HZ50V";
            decimalPlaces = 2;
            break;
        case symbolNames[8]: // "1HZ75V"
            randomIndex = "1HZ75V";
            decimalPlaces = 2;
            break;
        case symbolNames[9]: // "1HZ100V"
            randomIndex = "1HZ100V";
            decimalPlaces = 2;
            break;
        default:
            randomIndex = 'R_100';
            decimalPlaces = 2;
            break;
    }
}

/**
 * Handles menu element selection and symbol switching (based on original _0x5e5b48)
 */
function selectMenuElement(selectedElement) {
    // Remove active class from all menu elements (based on original pattern)
    var allMenuElements = document.querySelectorAll("#markt-menu > span");
    for (var i = 0; i < allMenuElements.length; i++) {
        allMenuElements[i].classList.remove("menu-active");
    }

    // Add active class to selected element
    selectedElement.classList.add("menu-active");

    // Update symbol configuration
    getCurrentSymbolConfig();
    console.log("Symbol changed to:", randomIndex, "with", decimalPlaces, "decimal places");

    // Update current symbol display
    var symbolDisplay = document.getElementById("currentSymbol");
    if (symbolDisplay) {
        symbolDisplay.textContent = "Current Market: " + randomIndex;
    }

    // Switch WebSocket subscription (following original pattern)
    if (websocket && websocket.readyState === WebSocket.OPEN) {
        console.log("Switching to symbol:", randomIndex);

        // Clear existing chart data (following original dps.length pattern)
        chartDataPoints = [];
        digitChartDataPoints = [];
        if (chart && chart.options && chart.options.data) {
            chart.options.data[0].dataPoints = [];
            chart.options.data[1].dataPoints = [];
            chart.render();
        }
        if (chart2 && chart2.options && chart2.options.data) {
            chart2.options.data[0].dataPoints = [];
            chart2.render();
        }

        // Unsubscribe and resubscribe (following original pattern)
        websocket.send(JSON.stringify({ 'forget': subscriptionId }));
        websocket.send(JSON.stringify({ 'forget_all': 'ticks' }));

        // Subscribe to new symbol
        setTimeout(function() {
            websocket.send(JSON.stringify({ 'ticks': randomIndex }));
            addLogMessage("<span style=\"color: #3f51b5;\">Connecting to market (" + randomIndex + ")</span>");
        }, 100);
    }
}

/**
 * Adds a log message to the output console
 */
function addLogMessage(message, borderType) {
    var output = document.getElementById("output");
    if (!output) return;
    
    var messageDiv = document.createElement("div");
    messageDiv.style.wordWrap = "break-word";
    messageDiv.style.fontSize = "97%";
    if (borderType == 't') messageDiv.style.borderTop = "thin dotted #000000";
    if (borderType == 'b') messageDiv.style.borderBottom = "thin dotted #000000";
    messageDiv.innerHTML = message;
    output.appendChild(messageDiv);
    output.insertBefore(messageDiv, output.childNodes[0]);
}

/**
 * Initialize WebSocket connection
 */
function initializeWebSocket() {
    websocket = new WebSocket(websocketUrls[0]);
    
    websocket.onopen = function() {
        console.log("WebSocket connected, subscribing to:", randomIndex);
        
        // Authorize with API token
        var apiToken = getCookie("api_token") || 'lX8QAY83KmTSa4P';
        websocket.send(JSON.stringify({ 'authorize': apiToken }));
    };
    
    websocket.onmessage = function(messageEvent) {
        responseData = JSON.parse(messageEvent.data);
        console.log("WebSocket message:", responseData);
        
        // Handle authorization
        if (responseData.msg_type === "authorize") {
            if (responseData.error) {
                addLogMessage("<span style=\"color: red;\">Authorization failed: " + responseData.error.message + "</span>");
            } else {
                var balance = responseData.authorize.balance;
                currency = responseData.authorize.currency;
                startingBalance = balance;
                addLogMessage("<span style=\"color: #4caf50;\">Connected to Deriv Market (" + randomIndex + ")</span>");
                
                // Update UI
                document.getElementById("displayBal").innerHTML = "Balance: " + balance + " " + currency;
                document.getElementById("accountdata").innerHTML = "Account: " + responseData.authorize.loginid;
                document.getElementById("logout-btn").style.display = "block";
                document.getElementById("login-btn").style.display = "none";
                document.getElementById("btm-tool").style.display = "block";
                
                // Subscribe to initial symbol
                websocket.send(JSON.stringify({ 'ticks': randomIndex }));
            }
        }
        
        // Handle tick data
        if (responseData.tick) {
            console.log("Tick received for:", responseData.echo_req.ticks, "Price:", responseData.tick.quote);
            
            if (responseData.echo_req.ticks === randomIndex) {
                subscriptionId = responseData.tick.id;
                
                // Request historical data
                websocket.send(JSON.stringify({
                    'ticks_history': randomIndex,
                    'end': "latest",
                    'start': 1,
                    'style': "ticks",
                    'count': 21
                }));
            }
        }
        
        // Handle historical data
        if (responseData.history) {
            console.log("History received for:", responseData.echo_req.ticks_history);
            
            if (responseData.echo_req.ticks_history === randomIndex) {
                processHistoricalData(responseData.history);
            }
        }
        
        // Handle errors
        if (responseData.error) {
            console.error("WebSocket Error:", responseData.error);
            addLogMessage("<span style=\"color: red;\">Error: " + responseData.error.message + "</span>");
        }
    };
    
    websocket.onerror = function(error) {
        console.error("WebSocket error:", error);
        addLogMessage("<span style=\"color: red;\">Connection error</span>");
    };
    
    websocket.onclose = function() {
        console.log("WebSocket closed");
        addLogMessage("<span style=\"color: orange;\">Connection closed</span>");
    };
}

/**
 * Process historical data and update charts
 */
function processHistoricalData(history) {
    console.log("Processing", history.prices.length, "historical prices for", randomIndex);

    // Clear existing data
    chartDataPoints = [];
    timestamps = [];
    spotPrices = [];
    digitValues = [];
    tickValues = [];
    thickValues = [];

    // Process historical data (following original pattern: time[_0x10b03f] = b.history.times[0x14 - _0x10b03f])
    for (var dataIndex = 0; dataIndex < 21; dataIndex++) {
        timestamps[dataIndex] = history.times[20 - dataIndex]; // 0x14 = 20
        spotPrices[dataIndex] = history.prices[20 - dataIndex];
        spotPrices[dataIndex] = Number(spotPrices[dataIndex]).toFixed(decimalPlaces);
        digitValues[dataIndex] = spotPrices[dataIndex].slice(-1);
    }

    // Prepare chart data points (following original pattern)
    for (var dataIndex = 0; dataIndex < 21; dataIndex++) {
        xValue = new Date(timestamps[dataIndex] * 1000); // 0x3e8 = 1000
        yValue = parseFloat(spotPrices[dataIndex]);

        if (dataIndex == 0) {
            markerType = 'circle';
        } else {
            markerType = 'none';
        }

        // Set marker color based on high/low values (following original pattern)
        if (yValue == Math.max.apply(null, spotPrices)) {
            markerColor = "#29abe2"; // Blue for high
            markerType = "circle";
        } else if (yValue == Math.min.apply(null, spotPrices)) {
            markerColor = "#c03"; // Red for low
            markerType = "circle";
        } else {
            markerColor = "#32cd32"; // Green for normal
        }

        chartDataPoints.push({
            'x': xValue,
            'y': yValue,
            'markerType': markerType,
            'markerColor': markerColor,
            'markerBorderColor': "#ccc"
        });
    }

    // Keep only the last 21 data points (following original pattern)
    if (chartDataPoints.length > 21) {
        while (chartDataPoints.length != 21) {
            chartDataPoints.shift();
        }
    }

    // Render chart and reverse arrays (following original pattern)
    chart.render();
    spotPrices.reverse();
    digitValues.reverse();
    updateChart(); // Call AI analysis function

    // Process digits and update UI (following original pattern: for (_0x10b03f = 0x1; _0x10b03f < 21; _0x10b03f++))
    for (var digitIndex = 1; digitIndex < 21; digitIndex++) {
        document.querySelector("#digits > span:nth-child(" + digitIndex + ')').innerHTML = digitValues[digitIndex];
        var currentPrice = parseFloat(spotPrices[20]); // 0x14 = 20

        // Determine price movement direction for header styling
        if (currentPrice == Math.max.apply(null, spotPrices)) {
            var priceDirection = 'up';
            var headerColor = "#29abe2";
        } else if (currentPrice == Math.min.apply(null, spotPrices)) {
            var priceDirection = "down";
            headerColor = '#c03';
        } else {
            var priceDirection = 'mid';
            headerColor = "#32cd32";
        }

        // Calculate digit movement and update animations (following original pattern)
        if (spotPrices[digitIndex - 1] < spotPrices[digitIndex]) {
            updateDigitAnimation(digitIndex, 'up');
            if (digitValues[digitIndex] != 0) {
                var digitMovement = digitValues[digitIndex] * 1;
            }
            if (digitValues[digitIndex - 1] > 5 && digitValues[digitIndex] == 0) {
                var digitMovement = 10; // 0xa = 10
            }
            if (digitValues[digitIndex - 1] <= 5 && digitValues[digitIndex] == 0) {
                var digitMovement = 0;
            }
        } else if (spotPrices[digitIndex - 1] > spotPrices[digitIndex]) {
            updateDigitAnimation(digitIndex, 'down');
            if (digitValues[digitIndex] != 0) {
                var digitMovement = digitValues[digitIndex] * -1;
            }
            if (digitValues[digitIndex - 1] > 5 && digitValues[digitIndex] == 0) {
                var digitMovement = -10; // -0xa = -10
            }
            if (digitValues[digitIndex - 1] <= 5 && digitValues[digitIndex] == 0) {
                var digitMovement = 0; // -0x0 = 0
            }
        } else if (spotPrices[digitIndex - 1] == spotPrices[digitIndex] && digitIndex - 1 > 0) {
            // Handle equal prices by copying previous movement
            if (document.querySelector("#digits > span:nth-child(" + (digitIndex - 1) + ')').className == "digits_moved_up") {
                updateDigitAnimation(digitIndex, 'up');
            } else if (document.querySelector("#digits > span:nth-child(" + (digitIndex - 1) + ')').className == "digits_moved_down") {
                updateDigitAnimation(digitIndex, "down");
            }
        }

        tickValues.shift(0);
        tickValues.push(digitMovement);
    }

    var chartColor = "#29abe2";
    thickValues.shift(0);
    thickValues.push(priceDirection);

    // Update digit chart data (following original pattern)
    for (var digitIndex = 1; digitIndex < 21; digitIndex++) {
        if (spotPrices[digitIndex - 1] < spotPrices[digitIndex]) {
            updateDigitAnimation(digitIndex, 'up');
            chartColor = '#29abe2';
        } else if (spotPrices[digitIndex - 1] > spotPrices[digitIndex]) {
            updateDigitAnimation(digitIndex, 'down');
            chartColor = "#c03";
        }
        updateHeaderAnimation(digitIndex, thickValues[digitIndex - 1]);
        document.querySelector("#headcol > span:nth-child(" + digitIndex + ')').innerHTML = tickValues;
        var xDigit = digitIndex;
        var yDigit = parseFloat(tickValues[digitIndex - 1]);

        digitChartDataPoints.push({
            'x': xDigit,
            'y': yDigit,
            'indexLabel': digitValues[digitIndex],
            'indexLabelFontWeight': "bold",
            'indexLabelFontSize': 11, // 0xb = 11
            'markerType': "circle",
            'markerColor': chartColor,
            'markerBorderColor': "#ccc"
        });
    }

    // Keep only the last 20 digit chart points (following original pattern)
    if (digitChartDataPoints.length > 21) {
        while (digitChartDataPoints.length != 20) { // 0x14 = 20
            digitChartDataPoints.shift();
        }
    }

    chart2.render();

    // Hide loading indicator
    if (isLoading) {
        document.getElementById("preloadingtxt").style.display = "none";
        isLoading = false;
    }

    console.log("Chart updated with", chartDataPoints.length, "data points");
    console.log("Digits updated:", digitValues.slice(0, 10));
}

/**
 * Update digits display and digit chart
 */
function updateDigitsDisplay() {
    // Update digits display
    for (var i = 1; i <= Math.min(20, digitValues.length); i++) {
        var digitElement = document.querySelector("#digits > span:nth-child(" + i + ')');
        if (digitElement && digitValues[i-1] !== undefined) {
            digitElement.innerHTML = digitValues[i-1];
        }
    }

    // Update digit chart
    digitChartDataPoints = [];
    for (var i = 1; i < Math.min(21, digitValues.length); i++) {
        digitChartDataPoints.push({
            'x': i,
            'y': parseFloat(digitValues[i] || 0),
            'indexLabel': digitValues[i],
            'indexLabelFontWeight': "bold",
            'indexLabelFontSize': 11,
            'markerType': "circle",
            'markerColor': "#29abe2",
            'markerBorderColor': "#ccc"
        });
    }

    // Render digit chart
    if (chart2 && chart2.options && chart2.options.data) {
        chart2.options.data[0].dataPoints = digitChartDataPoints;
        chart2.render();
    }
}

/**
 * Updates digit display animation based on price movement
 */
function updateDigitAnimation(position, direction) {
    var digitElement = document.querySelector("#digits > span:nth-child(" + position + ')');
    if (digitElement) {
        var currentClass = digitElement.className;
        if (currentClass != 'digits_moved_' + direction) {
            digitElement.classList.remove(currentClass);
            digitElement.classList.add("digits_moved_" + direction);
        }
    }
}

/**
 * Updates header column animation based on price movement
 */
function updateHeaderAnimation(position, direction) {
    var headerElement = document.querySelector("#headcol > span:nth-child(" + position + ')');
    if (headerElement) {
        var currentClass = headerElement.className;
        if (currentClass != "Head_moved_" + direction) {
            headerElement.classList.remove(currentClass);
            headerElement.classList.add("Head_moved_" + direction);
        }
    }
}

/**
 * Get cookie value
 */
function getCookie(name) {
    var match = document.cookie.match("(^|;) ?" + name + "=([^;]*)(;|$)");
    return match ? match[2] : null;
}

/**
 * Set cookie value
 */
function setCookie(name, value, days) {
    var expires = '';
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
        expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + '=' + (value || '') + expires + "; path=/";
}

// Initialize application when page loads (following original pattern)
window.addEventListener('load', function() {
    console.log("Page loaded, initializing application...");

    // Set up menu click listeners (following original rndMenu pattern)
    var rndMenu = document.querySelectorAll("#markt-menu > span");
    for (var i = 0; i < rndMenu.length; i++) {
        addMenuListener(rndMenu[i]);
    }

    function addMenuListener(menuElement) {
        menuElement.addEventListener("click", function() {
            selectMenuElement(menuElement);
        });
    }

    // Initialize symbol configuration
    getCurrentSymbolConfig();

    // Update initial symbol display
    var symbolDisplay = document.getElementById("currentSymbol");
    if (symbolDisplay) {
        symbolDisplay.textContent = "Current Market: " + randomIndex;
    }

    // Initialize charts
    initializeCharts();

    // Initialize WebSocket connection
    initializeWebSocket();

    console.log("Application initialized successfully");
});

/**
 * Initialize CanvasJS charts (following original pattern exactly)
 */
function initializeCharts() {
    // Main price chart (following original chart = new CanvasJS.Chart pattern)
    chart = new CanvasJS.Chart('chartContainer', {
        'animationEnabled': false,
        'theme': "light2",
        'title': { 'titleFontSize': 0, 'text': '' },
        'toolTip': {
            'enabled': true,
            'animationEnabled': true,
            'borderColor': "#ccc",
            'borderThickness': 1,
            'fontColor': '#000',
            'content': "{name}: {y}"
        },
        'axisX': {
            'includeZero': false,
            'titleFontSize': 0,
            'labelFontSize': 0,
            'gridThickness': 0,
            'tickLength': 0,
            'lineThickness': 1
        },
        'axisY': {
            'includeZero': false,
            'labelFontSize': 10,
            'gridThickness': 0,
            'lineThickness': 1
        },
        'legend': {
            'cursor': "pointer",
            'verticalAlign': "top",
            'horizontalAlign': 'left',
            'dockInsidePlotArea': true
        },
        'data': [{
            'type': "line",
            'name': "Price",
            'showInLegend': true,
            'lineColor': "#67636a",
            'lineThickness': 1,
            'markerType': 'none',
            'dataPoints': chartDataPoints
        }, {
            'type': "scatter",
            'name': "Signals",
            'showInLegend': false,
            'indexLabelTextAlign': 'left',
            'markerSize': 12,
            'dataPoints': []
        }]
    });

    // Digit chart (following original chart2 = new CanvasJS.Chart pattern)
    chart2 = new CanvasJS.Chart("chartContainerDigit", {
        'animationEnabled': false,
        'theme': "light2",
        'title': { 'titleFontSize': 0, 'text': '' },
        'toolTip': {
            'enabled': true,
            'animationEnabled': true,
            'borderColor': "#ccc",
            'borderThickness': 1,
            'fontColor': "#000",
            'content': "{y}"
        },
        'axisX': {
            'includeZero': false,
            'titleFontSize': 0,
            'labelFontSize': 0,
            'gridThickness': 0,
            'tickLength': 0,
            'lineThickness': 1
        },
        'axisY': {
            'stripLines': [{
                'startValue': -0.05,
                'endValue': 0.05,
                'color': 'black'
            }],
            'valueFormatString': "#000",
            'includeZero': false,
            'titleFontSize': 0,
            'labelFontSize': 0,
            'gridThickness': 0,
            'tickLength': 0,
            'lineThickness': 1
        },
        'data': [{
            'type': "line",
            'lineColor': '#ccc',
            'lineThickness': 1,
            'markerType': "none",
            'markerSize': 6,
            'markerBorderThickness': 0,
            'dataPoints': digitChartDataPoints
        }]
    });

    console.log("Charts initialized");
}

// Auto trading functions (called by AI)
function AutoPuTT() {
    var stakeAmount = Number(document.getElementById("stakeinp_auto").value);
    var formattedStake = stakeAmount.toFixed(2);
    websocket.send(JSON.stringify({
        'buy': '1', 'price': formattedStake,
        'parameters': {
            'amount': formattedStake, 'basis': 'stake', 'contract_type': "PUT",
            'currency': currency, 'duration': 5, 'duration_unit': 't', 'symbol': randomIndex
        }
    }));
}

function AutoCaLL() {
    var stakeAmount = Number(document.getElementById('stakeinp_auto').value);
    var formattedStake = stakeAmount.toFixed(2);
    websocket.send(JSON.stringify({
        'buy': '1', 'price': formattedStake,
        'parameters': {
            'amount': formattedStake, 'basis': "stake", 'contract_type': 'CALL',
            'currency': currency, 'duration': 5, 'duration_unit': 't', 'symbol': randomIndex
        }
    }));
}

// Utility functions
function loginclick() { window.location.href = '../login'; }
function logiout() { setCookie("api_token", '', 30); window.location.href = "/ldp"; }

/**
 * Called by AI analysis to update chart with signals
 */
function updateChart() {
    // This function is called by the AI analysis
    // The AI will add signal markers to chart.options.data[1].dataPoints
    console.log("updateChart called - AI analysis can add signals here");
}

/**
 * Opens a specific tab in the trading interface
 */
function openCity(tabName) {
    var tabcontent = document.getElementsByClassName("tabcontent");
    for (var i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }
    var tablinks = document.getElementsByClassName("tablinks");
    for (var i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }
    var targetTab = document.getElementById(tabName);
    if (targetTab) {
        targetTab.style.display = "block";
    }
}

// Debug functions
function testSymbolSwitching(targetSymbol) {
    console.log("Testing symbol switching to", targetSymbol);
    var menuElement = document.querySelector("#markt-menu > span[title='" + targetSymbol + "']");
    if (menuElement) {
        menuElement.click();
        console.log("Clicked menu for", targetSymbol);
    } else {
        console.log("Menu element not found for", targetSymbol);
    }
}

function debugUIElements() {
    console.log("=== UI Elements Debug ===");
    console.log("Digits container:", document.getElementById("digits"));
    console.log("Digit chart container:", document.getElementById("chartContainerDigit"));
    console.log("Digit values:", digitValues);
    console.log("Chart data points:", chartDataPoints.length);
    console.log("Digit chart data points:", digitChartDataPoints.length);

    // Test digits display
    var digitsContainer = document.getElementById("digits");
    if (digitsContainer) {
        var digitSpans = digitsContainer.querySelectorAll("span");
        console.log("Found", digitSpans.length, "digit spans");
        for (var i = 0; i < Math.min(5, digitSpans.length); i++) {
            console.log("Digit span", i, ":", digitSpans[i].innerHTML);
        }
    }

    // Test charts
    console.log("Main chart:", chart ? "initialized" : "not initialized");
    console.log("Digit chart:", chart2 ? "initialized" : "not initialized");
}

function forceUpdateDigits() {
    console.log("Force updating digits with test data");
    digitValues = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
    updateDigitsDisplay();
    console.log("Digits updated");
}
