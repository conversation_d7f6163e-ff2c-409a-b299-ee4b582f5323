let previousMarketCondition = null;
const volatilityHistory = [];
const trendStrengthHistory = [];
const marketConditionHistory = [];
function updateChart() {
  let _0x53a5ab = chart.options.data[0x0].dataPoints.reverse();
  let _0x4ec204 = calculateBollingerBands(_0x53a5ab);
  let _0x2cf5be = calculateATR(_0x53a5ab);
  let _0x39bd4d = calculateVolatility(_0x53a5ab);
  let _0x62e165 = calculateTrendStrength(_0x53a5ab);
  volatilityHistory.push(_0x39bd4d);
  trendStrengthHistory.push(_0x62e165);
  if (volatilityHistory.length > 0x32) {
    volatilityHistory.shift();
    trendStrengthHistory.shift();
  }
  let _0x225baa = checkMarketConditions(volatilityHistory, trendStrengthHistory);
  marketConditionHistory.push(_0x225baa);
  if (marketConditionHistory.length > 0xa) {
    marketConditionHistory.shift();
  }
  let _0x41e25b = calculateMarketConditionMA(marketConditionHistory);
  if (_0x41e25b !== previousMarketCondition) {
    console.log("Market condition changed to:", _0x41e25b);
    showNotify(getNotifyType(_0x41e25b), "Market condition: " + _0x41e25b);
    updateMarketConditionDisplay(_0x41e25b);
    previousMarketCondition = _0x41e25b;
  }
  let _0x530241 = generateSignal(_0x53a5ab, _0x4ec204, _0x2cf5be);
  if (_0x530241) {
    _0x530241.tickCount = 0x0;
    chart.options.data[0x1].dataPoints.push(_0x530241);
  }
  chart.options.data[0x1].dataPoints = chart.options.data[0x1].dataPoints.filter(_0x54f275 => {
    _0x54f275.tickCount++;
    return _0x54f275.tickCount <= 0xf;
  });
  chart.render();
}
function updateMarketConditionDisplay(_0xa24e0b) {
  const _0x19c88c = document.getElementById('market_condition');
  if (_0x19c88c) {
    _0x19c88c.textContent = _0xa24e0b;
    _0x19c88c.className = "market-condition " + _0xa24e0b.toLowerCase().replace(" ", '-');
  }
}
function checkMarketConditions(_0x5f2a40, _0x3a3297) {
  let _0x49ee58 = _0x5f2a40.reduce((_0x5b553e, _0x5d0611) => _0x5b553e + _0x5d0611) / _0x5f2a40.length;
  let _0x5a87f6 = _0x3a3297.reduce((_0x57b2fa, _0x4ef210) => _0x57b2fa + _0x4ef210) / _0x3a3297.length;
  let _0x5c064e = _0x5f2a40[_0x5f2a40.length - 0x1];
  let _0x4796f1 = _0x3a3297[_0x3a3297.length - 0x1];
  let _0x4ee98f = _0x49ee58 * 1.5;
  let _0xb12822 = _0x5a87f6 * 0.6;
  if (_0x5c064e > _0x4ee98f && _0x4796f1 < _0xb12822) {
    return -0x2;
  } else {
    if (_0x5c064e > _0x4ee98f) {
      return -0x1;
    } else {
      if (_0x4796f1 < _0xb12822) {
        return 0x0;
      } else {
        if (_0x5c064e < _0x49ee58 * 0.6 && _0x4796f1 > _0x5a87f6 * 1.5) {
          return 0x2;
        } else {
          return _0x5c064e < _0x49ee58 && _0x4796f1 > _0x5a87f6 ? 0x1 : 0x0;
        }
      }
    }
  }
}
function calculateMarketConditionMA(_0x421807) {
  let _0x15e138 = _0x421807.reduce((_0x576c66, _0x32507a) => _0x576c66 + _0x32507a, 0x0);
  let _0x3e287d = _0x15e138 / _0x421807.length;
  if (_0x3e287d <= -1.5) {
    return "Very Bad";
  }
  if (_0x3e287d <= -0.5) {
    return 'Bad';
  }
  if (_0x3e287d >= 1.5) {
    return "Very Good";
  }
  if (_0x3e287d >= 0.5) {
    return "Good";
  }
  return 'Neutral';
}
function getNotifyType(_0xcfd371) {
  switch (_0xcfd371) {
    case "Very Bad":
    case "Bad":
      return "error";
    case "Neutral":
      return "info";
    case "Good":
    case "Very Good":
      return "success";
    default:
      return "info";
  }
}
function calculateVolatility(_0x55f2fe, _0x290cea = 0xe) {
  if (_0x55f2fe.length < _0x290cea) {
    return null;
  }
  let _0x226972 = [];
  for (let _0x4baa45 = 0x1; _0x4baa45 < _0x55f2fe.length; _0x4baa45++) {
    _0x226972.push((_0x55f2fe[_0x4baa45].y - _0x55f2fe[_0x4baa45 - 0x1].y) / _0x55f2fe[_0x4baa45 - 0x1].y);
  }
  let _0x4dbb6f = _0x226972.reduce((_0x1a7a70, _0x9707c8) => _0x1a7a70 + _0x9707c8, 0x0) / _0x226972.length;
  let _0x39f4a8 = _0x226972.map(_0x52688d => Math.pow(_0x52688d - _0x4dbb6f, 0x2));
  let _0x70c99f = _0x39f4a8.reduce((_0x37d77a, _0x27d966) => _0x37d77a + _0x27d966, 0x0) / _0x39f4a8.length;
  return Math.sqrt(_0x70c99f) * Math.sqrt(0xfc);
}
function calculateTrendStrength(_0x5d64cd, _0x1ec761 = 0xe) {
  if (_0x5d64cd.length < _0x1ec761) {
    return null;
  }
  let _0x598748 = calculateSMA(_0x5d64cd, _0x1ec761);
  let _0x272540 = _0x598748[_0x598748.length - 0x1].y;
  let _0x25ffa4 = (_0x5d64cd[_0x5d64cd.length - 0x1].y - _0x5d64cd[_0x5d64cd.length - _0x1ec761].y) / _0x5d64cd[_0x5d64cd.length - _0x1ec761].y;
  return Math.abs(_0x25ffa4) * (_0x5d64cd[_0x5d64cd.length - 0x1].y / _0x272540);
}
function generateSignal(_0x225517, _0x54b947, _0x16b25e) {
  if (_0x225517.length < 0x2 || _0x54b947.length < 0x2 || _0x16b25e.length < 0x2) {
    console.log("Not enough data for signal generation");
    return null;
  }
  let _0x33997d = _0x225517.length - 0x1;
  let _0x37a737 = _0x54b947[_0x54b947.length - 0x1];
  let _0x230e8c = _0x54b947[_0x54b947.length - 0x2];
  let _0xa99cc = _0x16b25e[_0x16b25e.length - 0x1].y;
  if (_0x225517[_0x33997d].y > _0x37a737.lower && _0x225517[_0x33997d - 0x1].y <= _0x230e8c.lower && _0x225517[_0x33997d].y - _0x37a737.lower < _0xa99cc) {
    console.log("Buy signal generated");
    if (AUTO_TRADING_START && IS_CALL_TRADE) {
      IS_CALL_TRADE = false;
      AutoCaLL();
    }
    return {
      'x': _0x225517[_0x33997d].x,
      'y': _0x225517[_0x33997d].y,
      'markerColor': "#32CD32",
      'markerType': "none",
      'markerSize': 0xc,
      'indexLabel': '▲',
      'indexLabelFontColor': "#32CD32",
      'tickCount': 0x0,
      'indexLabelFontSize': 0x23
    };
  } else {
    if (_0x225517[_0x33997d].y < _0x37a737.upper && _0x225517[_0x33997d - 0x1].y >= _0x230e8c.upper && _0x37a737.upper - _0x225517[_0x33997d].y < _0xa99cc) {
      console.log("Sell signal generated");
      if (AUTO_TRADING_START && IS_CALL_TRADE) {
        IS_CALL_TRADE = false;
        AutoPuTT();
      }
      return {
        'x': _0x225517[_0x33997d].x,
        'y': _0x225517[_0x33997d].y,
        'markerColor': "#FF0000",
        'markerType': "none",
        'markerSize': 0xc,
        'indexLabel': '▼',
        'indexLabelFontColor': "#FF0000",
        'tickCount': 0x0,
        'indexLabelFontSize': 0x23
      };
    }
  }
  return null;
}
function calculateBollingerBands(_0x2dbe01, _0xd65ffd = 0x14, _0x152dcd = 0x2) {
  let _0x956e6e = calculateSMA(_0x2dbe01, _0xd65ffd);
  let _0x56a97b = [];
  for (let _0x1c6694 = _0xd65ffd - 0x1; _0x1c6694 < _0x2dbe01.length; _0x1c6694++) {
    let _0x2f5f83 = 0x0;
    for (let _0x2a9b93 = 0x0; _0x2a9b93 < _0xd65ffd; _0x2a9b93++) {
      _0x2f5f83 += Math.pow(_0x2dbe01[_0x1c6694 - _0x2a9b93].y - _0x956e6e[_0x1c6694 - _0xd65ffd + 0x1].y, 0x2);
    }
    let _0x5a510d = Math.sqrt(_0x2f5f83 / _0xd65ffd);
    _0x56a97b.push({
      'x': _0x2dbe01[_0x1c6694].x,
      'middle': _0x956e6e[_0x1c6694 - _0xd65ffd + 0x1].y,
      'upper': _0x956e6e[_0x1c6694 - _0xd65ffd + 0x1].y + _0x152dcd * _0x5a510d,
      'lower': _0x956e6e[_0x1c6694 - _0xd65ffd + 0x1].y - _0x152dcd * _0x5a510d
    });
  }
  return _0x56a97b;
}
function calculateATR(_0x7e871f, _0x4ea522 = 0xe) {
  let _0x5157ea = [];
  let _0x252b44 = [];
  for (let _0x416b06 = 0x1; _0x416b06 < _0x7e871f.length; _0x416b06++) {
    let _0x4e98dc = _0x7e871f[_0x416b06].y;
    let _0x23f50a = _0x7e871f[_0x416b06].y;
    let _0x355b52 = _0x7e871f[_0x416b06 - 0x1].y;
    let _0x210215 = _0x4e98dc - _0x23f50a;
    let _0x59a936 = Math.abs(_0x4e98dc - _0x355b52);
    let _0x10ff2e = Math.abs(_0x23f50a - _0x355b52);
    _0x5157ea.push(Math.max(_0x210215, _0x59a936, _0x10ff2e));
  }
  let _0x5d8e48 = _0x5157ea.slice(0x0, _0x4ea522).reduce((_0x7cf21, _0x505db3) => _0x7cf21 + _0x505db3) / _0x4ea522;
  _0x252b44.push({
    'x': _0x7e871f[_0x4ea522].x,
    'y': _0x5d8e48
  });
  for (let _0x29cdc2 = _0x4ea522 + 0x1; _0x29cdc2 < _0x7e871f.length; _0x29cdc2++) {
    let _0x29c5f2 = (_0x252b44[_0x252b44.length - 0x1].y * (_0x4ea522 - 0x1) + _0x5157ea[_0x29cdc2 - 0x1]) / _0x4ea522;
    _0x252b44.push({
      'x': _0x7e871f[_0x29cdc2].x,
      'y': _0x29c5f2
    });
  }
  return _0x252b44;
}
function calculateSMA(_0x239f27, _0x3fa42c) {
  let _0x2aea13 = [];
  if (_0x239f27.length < _0x3fa42c) {
    return _0x2aea13;
  }
  for (let _0x390a0e = _0x3fa42c - 0x1; _0x390a0e < _0x239f27.length; _0x390a0e++) {
    let _0x4fa4fc = 0x0;
    for (let _0x1480ae = 0x0; _0x1480ae < _0x3fa42c; _0x1480ae++) {
      _0x4fa4fc += _0x239f27[_0x390a0e - _0x1480ae].y;
    }
    _0x2aea13.push({
      'x': _0x239f27[_0x390a0e].x,
      'y': _0x4fa4fc / _0x3fa42c
    });
  }
  return _0x2aea13;
}