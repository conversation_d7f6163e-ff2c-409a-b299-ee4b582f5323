var _0x26a1 = ['wss://ws.derivws.com/websockets/v3?app_id=21317'];
var ws;
var b;
var rnd;
var spot;
var time;
var dps;
var dps2;
var xd;
var digit;
var random;
var id;
var str;
var chart;
var xVal;
var yVal;
var mType;
var mColor;
var rndMenu;
str = ["R_100", "R_10", "R_25", 'R_50', "R_75", "1HZ10V", "1HZ25V", "1HZ50V", '1HZ75V', "1HZ100V"];
thick = [0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0];
dps = [];
dps2 = [];
time = [0x0];
spot = [0x0];
tic = [0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0];
digit = [0x0];
mType = "none";
mColor = "#32cd32";
'EN';
xVal = 0x0;
yVal = 0x0;
0x14;
var currency = "USD";
var isloading = true;
var starting_balance = 0x0;
var last_contract_id = '';
var AUTO_TRADING_START = false;
var IS_CALL_TRADE = true;
var last_trade = true;
var IS_OPEN = false;
const notyf = new Notyf({
  'duration': 0x9c4,
  'position': {
    'x': 'left',
    'y': "top"
  },
  'types': [{
    'type': "error",
    'background': 'indianred',
    'dismissible': true
  }, {
    'type': "warning",
    'background': "#ff9800",
    'dismissible': true,
    'icon': {
      'className': "fa fa-exclamation-triangle notiy-icon",
      'tagName': 'i',
      'text': ''
    }
  }, {
    'type': "info",
    'background': "#29abe2",
    'dismissible': true,
    'icon': {
      'className': "fa fa-info-circle notiy-icon",
      'tagName': 'i',
      'text': ''
    }
  }, {
    'type': "success",
    'background': "#3dc663",
    'dismissible': true
  }]
});
function showNotify(_0x9dd9ea, _0x117bd2) {
  notyf.open({
    'type': _0x9dd9ea,
    'message': _0x117bd2
  });
}
rndMenu = document.querySelectorAll("div.menu > span");
window.addEventListener('load', function () {
  for (var _0xb96de = 0x0; _0xb96de < rndMenu.length; _0xb96de++) {
    _0x3899bf(rndMenu[_0xb96de]);
  }
  if (_0x50f14f) {}
  function _0x3899bf(_0x4be76d) {
    _0x4be76d.addEventListener("click", function () {
      _0x5e5b48(_0x4be76d);
    });
  }
  function _0x3d7f30(_0x2c5c22, _0x370c87) {
    var _0x16471e = document.querySelector("#digits > span:nth-child(" + _0x2c5c22 + ')').className;
    if (_0x16471e != 'digits_moved_' + _0x370c87) {
      document.querySelector("#digits > span:nth-child(" + _0x2c5c22 + ')').classList.remove(_0x16471e);
      document.querySelector("#digits > span:nth-child(" + _0x2c5c22 + ')').classList.add("digits_moved_" + _0x370c87);
    }
  }
  function _0x2d5059(_0x7a94, _0x32b63f) {
    var _0x32efc0 = document.querySelector("#headcol > span:nth-child(" + _0x7a94 + ')').className;
    if (_0x32efc0 != "Head_moved_" + _0x32b63f) {
      document.querySelector("#headcol > span:nth-child(" + _0x7a94 + ')').classList.remove(_0x32efc0);
      document.querySelector("#headcol > span:nth-child(" + _0x7a94 + ')').classList.add("Head_moved_" + _0x32b63f);
    }
  }
  function _0xa11c87() {
    random = document.querySelector("body > div.menu > span.menu-active").title;
    switch (random) {
      case str[0x0]:
        rnd = "R_100";
        xd = 0x2;
        break;
      case str[0x1]:
        rnd = "R_10";
        xd = 0x3;
        break;
      case str[0x2]:
        rnd = "R_25";
        xd = 0x3;
        break;
      case str[0x3]:
        rnd = "R_50";
        xd = 0x4;
        break;
      case str[0x4]:
        rnd = "R_75";
        xd = 0x4;
        break;
      case str[0x5]:
        rnd = "1HZ10V";
        xd = 0x2;
        break;
      case str[0x6]:
        rnd = "1HZ25V";
        xd = 0x2;
        break;
      case str[0x7]:
        rnd = "1HZ50V";
        xd = 0x2;
        break;
      case str[0x8]:
        rnd = "1HZ75V";
        xd = 0x2;
        break;
      case str[0x9]:
        rnd = "1HZ100V";
        xd = 0x2;
        break;
      default:
        rnd = 'R';
        xd = 0x0;
        break;
    }
  }
  _0xa11c87();
  ws = new WebSocket(_0x26a1[0x0]);
  output = document.getElementById("output");
  var _0x48b196 = '';
  function _0x13501e(_0x1463f0) {
    _0x48b196 = " " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ";
    _0x4b68b5(_0x48b196 + "Try to Connecting...");
    ws.send(JSON.stringify({
      'authorize': _0x1463f0
    }));
  }
  ws.onopen = function (_0x5d52b1) {
    ws.send(JSON.stringify({
      'ticks': rnd
    }));
  };
  function _0x4b68b5(_0x3bfc9a, _0x4ab453) {
    var _0x44862f = document.createElement("div");
    _0x44862f.style.wordWrap = "break-word";
    _0x44862f.style.fontSize = "97%";
    if (_0x4ab453 == 't') {
      _0x44862f.style.borderTop = "thin dotted #000000";
    }
    if (_0x4ab453 == 'b') {
      _0x44862f.style.borderBottom = "thin dotted #000000";
    }
    _0x44862f.innerHTML = _0x3bfc9a;
    output.appendChild(_0x44862f);
    output.insertBefore(_0x44862f, output.childNodes[0x0]);
  }
  ws.onmessage = function (_0x23351f) {
    b = JSON.parse(_0x23351f.data);
    if (b.tick) {
      _0xa11c87();
      if (b.echo_req.ticks == rnd) {
        id = b.tick.id;
        ws.send(JSON.stringify({
          'ticks_history': rnd,
          'end': "latest",
          'start': 0x1,
          'style': "ticks",
          'count': 21
        }));
      } else {
        ws.send(JSON.stringify({
          'forget': id
        }));
        ws.send(JSON.stringify({
          'forget_all': 'ticks'
        }));
        ws.send(JSON.stringify({
          'ticks': rnd
        }));
        _0x4b68b5("<span style=\"color: #3f51b5;\">" + _0x48b196 + "Connecting to market (" + rnd + ")</span>");
      }
      ;
    }
    ;
    if (b.history) {
      if (b.echo_req.ticks_history == rnd) {
        if (document.querySelector("#chartContainer > div > a")) {
          if (document.querySelector("#chartContainer > div > a").innerText != "            ") {
            document.querySelector("#chartContainer > div > a").innerHTML = "            ";
            document.querySelector("#chartContainer > div > a").href = "https://www.binarybot.live";
          }
        }
        if (document.querySelector("#chartContainerDigit > div > a")) {
          if (document.querySelector("#chartContainerDigit > div > a").innerText != "        ") {
            document.querySelector("#chartContainerDigit > div > a").innerHTML = "          ";
            document.querySelector("#chartContainerDigit > div > a").href = 'https://www.binarybot.live';
          }
        }
        for (var _0x10b03f = 0x0; _0x10b03f < 21; _0x10b03f++) {
          time[_0x10b03f] = b.history.times[0x14 - _0x10b03f];
          spot[_0x10b03f] = b.history.prices[0x14 - _0x10b03f];
          spot[_0x10b03f] = Number(spot[_0x10b03f]).toFixed(xd);
          digit[_0x10b03f] = spot[_0x10b03f].slice(-0x1);
        }
        for (var _0x10b03f = 0x0; _0x10b03f < 21; _0x10b03f++) {
          xVal = new Date(time[_0x10b03f] * 0x3e8);
          yVal = parseFloat(spot[_0x10b03f]);
          if (_0x10b03f == 0x0) {
            mType = 'circle';
          } else {
            mType = 'none';
          }
          if (yVal == Math.max.apply(null, spot)) {
            mColor = "#29abe2";
            mType = "circle";
          } else if (yVal == Math.min.apply(null, spot)) {
            mColor = "#c03";
            mType = "circle";
          } else {
            mColor = "#32cd32";
          }
          dps.push({
            'x': xVal,
            'y': yVal,
            'markerType': mType,
            'markerColor': mColor,
            'markerBorderColor': "#ccc"
          });
        }
        if (dps.length > 21) {
          while (dps.length != 21) {
            dps.shift();
          }
        }
        chart.render();
        spot.reverse();
        digit.reverse();
        updateChart();
        for (var _0x10b03f = 0x1; _0x10b03f < 21; _0x10b03f++) {
          document.querySelector("#digits > span:nth-child(" + _0x10b03f + ')').innerHTML = digit[_0x10b03f];
          yVal2 = parseFloat(spot[0x14]);
          if (yVal2 == Math.max.apply(null, spot)) {
            var _0x1cbef2 = 'up';
            mColorHead = "#29abe2";
          } else {
            if (yVal2 == Math.min.apply(null, spot)) {
              var _0x1cbef2 = "down";
              mColorHead = '#c03';
            } else {
              var _0x1cbef2 = 'mid';
              mColorHead = "#32cd32";
            }
          }
          if (spot[_0x10b03f - 0x1] < spot[_0x10b03f]) {
            _0x3d7f30(_0x10b03f, 'up');
            if (digit[_0x10b03f] != 0x0) {
              var _0x2191da = digit[_0x10b03f] * 0x1;
            }
            if (digit[_0x10b03f - 0x1] > 0x5 && digit[_0x10b03f] == 0x0) {
              var _0x2191da = 0xa;
            }
            if (digit[_0x10b03f - 0x1] <= 0x5 && digit[_0x10b03f] == 0x0) {
              var _0x2191da = 0x0;
            }
          } else {
            if (spot[_0x10b03f - 0x1] > spot[_0x10b03f]) {
              _0x3d7f30(_0x10b03f, 'down');
              if (digit[_0x10b03f] != 0x0) {
                var _0x2191da = digit[_0x10b03f] * -0x1;
              }
              if (digit[_0x10b03f - 0x1] > 0x5 && digit[_0x10b03f] == 0x0) {
                var _0x2191da = -0xa;
              }
              if (digit[_0x10b03f - 0x1] <= 0x5 && digit[_0x10b03f] == 0x0) {
                var _0x2191da = -0x0;
              }
            } else {
              if (spot[_0x10b03f - 0x1] == spot[_0x10b03f] && _0x10b03f - 0x1 > 0x0) {
                if (document.querySelector("#digits > span:nth-child(" + (_0x10b03f - 0x1) + ')').className == "digits_moved_up") {
                  _0x3d7f30(_0x10b03f, 'up');
                } else if (document.querySelector("#digits > span:nth-child(" + (_0x10b03f - 0x1) + ')').className == "digits_moved_down") {
                  _0x3d7f30(_0x10b03f, "down");
                }
              }
            }
          }
          tic.shift(0x0);
          tic.push(_0x2191da);
        }
        var _0x226420 = "#29abe2";
        thick.shift(0x0);
        thick.push(_0x1cbef2);
        for (var _0x10b03f = 0x1; _0x10b03f < 21; _0x10b03f++) {
          if (spot[_0x10b03f - 0x1] < spot[_0x10b03f]) {
            _0x3d7f30(_0x10b03f, 'up');
            _0x226420 = '#29abe2';
          } else if (spot[_0x10b03f - 0x1] > spot[_0x10b03f]) {
            _0x3d7f30(_0x10b03f, 'down');
            _0x226420 = "#c03";
          }
          _0x2d5059(_0x10b03f, thick[_0x10b03f - 0x1]);
          document.querySelector("#headcol > span:nth-child(" + _0x10b03f + ')').innerHTML = tic;
          xDigit = _0x10b03f;
          yDigit = parseFloat(tic[_0x10b03f - 0x1]);
          xDigit = _0x10b03f;
          yDigit = parseFloat(tic[_0x10b03f - 0x1]);
          dps2.push({
            'x': xDigit,
            'y': yDigit,
            'indexLabel': digit[_0x10b03f],
            'indexLabelFontWeight': "bold",
            'indexLabelFontSize': 0xb,
            'markerType': "circle",
            'markerColor': _0x226420,
            'markerBorderColor': "#ccc"
          });
        }
        if (dps2.length > 21) {
          while (dps2.length != 0x14) {
            dps2.shift();
          }
        }
        chart2.render();
        tic1 = tic[0x13];
        tic2 = tic[0x12];
        tic3 = tic[0x11];
        tic4 = tic[0x10];
        tic5 = tic[0xf];
        tic6 = tic[0xe];
        if (isloading) {
          document.getElementById("preloadingtxt").style.display = "none";
          isloading = false;
          if (_0x50f14f("api_token")) {
            setTimeout(function () {
              _0x13501e(_0x50f14f("api_token"));
            }, 0x3e8);
          } else {
            _0x4b68b5("<span style=\"color: #000000;\">" + _0x48b196 + "Please log in to your Deriv account to start trading..</span>", 't');
            document.getElementById("logout-btn").style.display = "none";
            document.getElementById("login-btn").style.display = "block";
            document.getElementById("mobile-login").style.display = "block";
            document.getElementById("btm-tool").style.display = 'block';
            document.getElementById("startbtn_auto").classList.add('disabledbtn');
            openCity("Auto");
          }
        }
      }
      ;
    }
    ;
    var _0x282abb = b.msg_type;
    if (isloading) {}
    if (b.error && b.error.code != 'AlreadySubscribed') {
      _0x4b68b5("<span style=\"color: red;\"> " + (" " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ") + '' + b.error.message + "</span>", 'b');
      if (AUTO_TRADING_START) {
        _0x1e1844();
      }
    }
    if (_0x282abb == "authorize") {
      if (b.error) {
        _0x4b68b5("<span style=\"color: #ffffff;\">" + _0x48b196 + "Please log in to your Deriv account to start trading..</span>", 't');
        document.getElementById("login-btn").style.display = 'block';
        document.getElementById("mobile-login").style.display = 'block';
        document.getElementById("btm-tool").style.display = "block";
        openCity("Auto");
      } else {
        var _0x14f114 = b.authorize.balance;
        var _0x31c102 = b.authorize.email;
        currency = b.authorize.currency;
        console.log("----------currency------", currency);
        sessionStorage.balanceStarting = _0x14f114;
        starting_balance = _0x14f114;
        _0x4b68b5("<span style=\"color: #4caf50;\">" + _0x48b196 + "Connected To The Deriv Market " + rnd + "</span>", 't');
        _0x4b68b5("<span style=\"color: #424242;\">" + _0x48b196 + "Waiting for Start trading.</span>");
        chart.options.data[0x1].dataPoints = [];
        var _0x18590f = b.authorize.fullname;
        var _0x13aeb7 = b.authorize.is_virtual;
        var _0x39534c = b.authorize.loginid;
        sessionStorage.fullnameSaved = _0x18590f;
        sessionStorage.cr_accountSaved = _0x39534c;
        sessionStorage.virtual_accSaved = _0x13aeb7;
        sessionStorage.emailSaved = _0x31c102;
        if (_0x13aeb7 == '0') {
          sessionStorage.accountType = "RealAccount";
        } else {
          sessionStorage.accountType = 'VirtualAccount';
        }
        sessionStorage.balanceNow = Number(_0x14f114);
        document.getElementById("displayBal").innerHTML = "Balance: " + sessionStorage.balanceNow + " " + currency;
        document.getElementById("accountdata").innerHTML = "Account: " + sessionStorage.cr_accountSaved;
        document.getElementById("logout-btn").style.display = "block";
        document.getElementById("btm-tool").style.display = "block";
        document.getElementById("login-btn").style.display = "none";
        document.getElementById("mobile-login").style.display = 'none';
        openCity("Auto");
        _0x3d905c();
      }
    } else {
      if (_0x282abb == "balance") {
        var _0x3f6037 = b.balance?.["balance"];
        var _0x24735e = sessionStorage.balanceNow;
        if (typeof Storage !== 'undefined') {
          sessionStorage.balanceNow = Number(_0x3f6037);
        }
        var _0x122eed = _0x3f6037 - _0x24735e;
        var _0x9ce74c = _0x122eed.toFixed(0x2);
        tradeprofit = Math.abs(_0x122eed - sessionStorage.modalOrder);
        profit = _0x3f6037 - starting_balance;
        if (_0x9ce74c > 0x0) {
          var _0x30f92f = " " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ";
          _0x4b68b5("<span style=\"color: #4CAF50;\"> " + _0x30f92f + '' + last_contract_id + " PROFIT (" + tradeprofit.toFixed(0x2) + ") " + currency + "</span>");
          last_trade = true;
          _0x52f0dc(_0x3f6037);
        } else {
          if (_0x9ce74c == 0x0 && !isNaN(_0x122eed) && _0x3f6037 != starting_balance) {
            last_trade = false;
            _0x52f0dc(_0x3f6037);
            var _0x30f92f = " " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ";
            _0x4b68b5("<span style=\"color: #f20202;\"> " + _0x30f92f + '' + last_contract_id + " LOSS (" + tradeprofit.toFixed(0x2) + ") " + currency + "</span>");
          }
        }
      } else {
        if (_0x282abb == "buy") {
          IS_OPEN = true;
          _0x268ffc(0x1);
          var _0x7ca746 = b.buy;
          last_contract_id = _0x7ca746.contract_id;
          _0x48b196 = " " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ";
          _0x4b68b5("<span style=\"color: #122212;\"> " + _0x48b196 + '' + last_contract_id + ": (" + _0x7ca746.buy_price + ") " + _0x7ca746.longcode + " ticks</span>");
        }
      }
    }
  };
  function _0x52f0dc(_0xd14522) {
    IS_OPEN = false;
    document.getElementById("displayBal").innerHTML = "Balance: " + _0xd14522 + " " + currency;
    if (profit > 0x0) {
      color = '#4CAF50';
    } else {
      if (profit == 0x0) {
        color = "black";
      } else {
        color = "#f20202";
      }
    }
    document.getElementById('displayProfitldp').innerHTML = "<span style=\"color: " + color + ";\"> Profit: " + profit.toFixed(0x2) + " " + currency + "</span>";
    var _0x3a0f35 = document.getElementById("Auto-btn").classList;
    if (_0x3a0f35.contains("button-active")) {
      IS_CALL_TRADE = true;
      if (profit >= sessionStorage.targetProft) {
        _0x4b68b5("<span style=\"color: #00720e; font-weight:700;\"> " + (" " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ") + "Congratulations! (" + profit.toFixed(0x2) + ") The target profit was reached. </span>", 'b');
        AUTO_TRADING_START = false;
        _0x1e1844();
      } else if (Math.abs(profit) >= sessionStorage.stopLoss) {
        AUTO_TRADING_START = false;
        _0x4b68b5("<span style=\"color: #ff2626; font-weight:700;\"> " + (" " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ") + "Sorry!! , Stop loss reached Please stop trading and come back later. </span>", 'b');
        _0x1e1844();
      } else {
        _0x5b0907();
      }
    } else {
      _0x1e1844();
    }
  }
  function _0x5b0907() {
    if (last_trade) {
      var _0x36278d = Number(document.getElementById("stakeinp_auto").value);
      var _0x43d312 = _0x36278d.toFixed(0x2);
      sessionStorage.modalOrder = _0x43d312;
    } else {
      var _0x40d166 = (sessionStorage.modalOrder * 2.5).toFixed(0x2);
      sessionStorage.modalOrder = Math.abs(_0x40d166);
    }
    if (sessionStorage.modalOrder < 0.5) {
      sessionStorage.modalOrder = 0x1;
    }
    document.getElementById('stakeinp_auto').value = sessionStorage.modalOrder;
    if (AUTO_TRADING_START) {
      _0x268ffc(0x0);
      var _0x3db289 = 0x1;
      if (!last_trade) {
        _0x3db289 = Math.floor(0x5 + Math.random() * 6);
        _0x4b68b5("<span style=\"color: #9c27b0;\">" + _0x48b196 + "Lets pause for a moment and wait.</span>", 't');
      }
    } else {
      _0x1e1844();
    }
  }
  function _0x2b039b(_0x127915) {
    if (_0x127915) {
      document.getElementById("Diff-btn").classList.add('disabledbtn');
      document.getElementById("Over-btn").classList.add("disabledbtn");
      document.getElementById("Rise-btn").classList.add("disabledbtn");
      document.getElementById("markt-menu").classList.add('disabledbtn');
      document.getElementById("stopbtn_auto").style.display = "inline-block";
      document.getElementById("startbtn_auto").style.display = "none";
    } else {
      document.getElementById('Diff-btn').classList.remove("disabledbtn");
      document.getElementById("Over-btn").classList.remove("disabledbtn");
      document.getElementById("Rise-btn").classList.remove("disabledbtn");
      document.getElementById('markt-menu').classList.remove('disabledbtn');
      document.getElementById("stopbtn_auto").style.display = "none";
      document.getElementById("startbtn_auto").style.display = 'inline-block';
    }
    document.getElementById("startbtn_auto").classList.remove('disabledbtn');
    document.getElementById("stopbtn_auto").classList.remove("disabledbtn");
  }
  function _0x268ffc(_0x408bfd) {
    if (_0x408bfd == '0') {
      document.getElementById("live-indicator-analyzing").style.display = "inline-block";
      document.getElementById("live-indicator-buy").style.display = "none";
    } else if (_0x408bfd == '1') {
      document.getElementById("live-indicator-analyzing").style.display = 'none';
      document.getElementById('live-indicator-buy').style.display = "inline-block";
    } else {
      document.getElementById('live-indicator-analyzing').style.display = "none";
      document.getElementById("live-indicator-buy").style.display = "none";
    }
  }
  function _0x1e1844() {
    _0x268ffc(0x3);
    _0x2b039b(AUTO_TRADING_START);
    _0x4b68b5("<span style=\"color: #625959;\"> " + (" " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ") + "Trading Stopped : WebSocket Closed. </span>", 'b');
    document.getElementById('stakeinp_auto').value = 0x1;
  }
  function _0x3d905c() {
    ws.send(JSON.stringify({
      'balance': 0x1,
      'subscribe': 0x1
    }));
  }
  document.getElementById('callbtn').addEventListener("click", function () {
    _0x48fc1a();
  });
  document.getElementById("fallbtn").addEventListener("click", function () {
    _0xc985e0();
  });
  document.getElementById("overbtn").addEventListener("click", function () {
    _0x1233ce();
  });
  document.getElementById("underbtn").addEventListener('click', function () {
    _0x1d1e4e();
  });
  document.getElementById("diffbtn").addEventListener('click', function () {
    _0x18626e();
  });
  document.getElementById("matchbtn").addEventListener("click", function () {
    _0x1a92dd();
  });
  document.getElementById('startbtn_auto').addEventListener('click', function () {
    sessionStorage.stopLoss = Number(document.getElementById('slinp_auto').value);
    sessionStorage.targetProft = Number(document.getElementById("tpinp_auto").value);
    sessionStorage.modalOrder = Number(document.getElementById("stakeinp_auto").value);
    AUTO_TRADING_START = true;
    _0x2b039b(AUTO_TRADING_START);
    _0x268ffc(0x0);
    document.getElementById('startbtn_auto').classList.add('disabledbtn');
  });
  document.getElementById("stopbtn_auto").addEventListener('click', function () {
    AUTO_TRADING_START = false;
    document.getElementById('stopbtn_auto').classList.add("disabledbtn");
    if (!IS_OPEN) {
      _0x1e1844();
    }
  });
  function _0xc985e0() {
    var _0x21cc22 = Number(document.getElementById('stakeinp_rise').value);
    var _0x20205b = _0x21cc22.toFixed(0x2);
    sessionStorage.modalOrder = _0x20205b;
    var _0x209171 = document.getElementById("ticksint_rise").value;
    ws.send(JSON.stringify({
      'buy': '1',
      'price': _0x20205b,
      'parameters': {
        'amount': _0x20205b,
        'basis': "stake",
        'contract_type': 'PUT',
        'currency': currency,
        'duration': _0x209171,
        'duration_unit': 't',
        'symbol': rnd
      }
    }));
  }
  function _0x48fc1a() {
    var _0x38cd96 = Number(document.getElementById("stakeinp_rise").value);
    var _0x5648d5 = _0x38cd96.toFixed(0x2);
    sessionStorage.modalOrder = _0x5648d5;
    var _0x4c275a = document.getElementById("ticksint_rise").value;
    ws.send(JSON.stringify({
      'buy': '1',
      'price': _0x5648d5,
      'parameters': {
        'amount': _0x5648d5,
        'basis': 'stake',
        'contract_type': "CALL",
        'currency': currency,
        'duration': _0x4c275a,
        'duration_unit': 't',
        'symbol': rnd
      }
    }));
  }
  function _0x18626e() {
    var _0x14f7ed = Number(document.getElementById("stakeinp_diff").value);
    var _0x8a63eb = _0x14f7ed.toFixed(0x2);
    sessionStorage.modalOrder = _0x8a63eb;
    var _0x4c2bba = document.getElementById('prediction_diff').value;
    ws.send(JSON.stringify({
      'buy': '1',
      'price': _0x8a63eb,
      'parameters': {
        'amount': _0x8a63eb,
        'basis': "stake",
        'contract_type': "DIGITDIFF",
        'currency': currency,
        'duration': 0x1,
        'duration_unit': 't',
        'symbol': rnd,
        'barrier': _0x4c2bba
      }
    }));
  }
  function _0x1a92dd() {
    var _0x541f56 = Number(document.getElementById('stakeinp_diff').value);
    var _0x4eea2a = _0x541f56.toFixed(0x2);
    sessionStorage.modalOrder = _0x4eea2a;
    var _0x46461d = document.getElementById("prediction_diff").value;
    ws.send(JSON.stringify({
      'buy': '1',
      'price': _0x4eea2a,
      'parameters': {
        'amount': _0x4eea2a,
        'basis': 'stake',
        'contract_type': "DIGITMATCH",
        'currency': currency,
        'duration': 0x1,
        'duration_unit': 't',
        'symbol': rnd,
        'barrier': _0x46461d
      }
    }));
  }
  function _0x1233ce() {
    var _0x5b71ce = Number(document.getElementById("stakeinp_over").value);
    var _0x486f01 = _0x5b71ce.toFixed(0x2);
    sessionStorage.modalOrder = _0x486f01;
    var _0x3aedb2 = document.getElementById("ticksint_over").value;
    var _0xc6bdde = document.getElementById("prediction_over").value;
    ws.send(JSON.stringify({
      'buy': '1',
      'price': _0x486f01,
      'parameters': {
        'amount': _0x486f01,
        'basis': "stake",
        'contract_type': 'DIGITOVER',
        'currency': currency,
        'duration': _0x3aedb2,
        'duration_unit': 't',
        'symbol': rnd,
        'barrier': _0xc6bdde
      }
    }));
  }
  function _0x1d1e4e() {
    var _0x241e12 = Number(document.getElementById("stakeinp_over").value);
    var _0x402e14 = _0x241e12.toFixed(0x2);
    sessionStorage.modalOrder = _0x402e14;
    var _0xf9f423 = document.getElementById("ticksint_over").value;
    var _0x2a9392 = document.getElementById('prediction_over').value;
    ws.send(JSON.stringify({
      'buy': '1',
      'price': _0x402e14,
      'parameters': {
        'amount': _0x402e14,
        'basis': "stake",
        'contract_type': 'DIGITUNDER',
        'currency': currency,
        'duration': _0xf9f423,
        'duration_unit': 't',
        'symbol': rnd,
        'barrier': _0x2a9392
      }
    }));
  }
  function _0x50f14f(_0xac05fb) {
    var _0x52f782 = document.cookie.match("(^|;) ?" + _0xac05fb + "=([^;]*)(;|$)");
    return _0x52f782 ? _0x52f782[0x2] : null;
  }
  chart = new CanvasJS.Chart('chartContainer', {
    'animationEnabled': false,
    'theme': "light2",
    'title': {
      'titleFontSize': 0x0,
      'text': ''
    },
    'toolTip': {
      'enabled': true,
      'animationEnabled': true,
      'borderColor': "#ccc",
      'borderThickness': 0x1,
      'fontColor': '#000',
      'content': "{name}: {y}"
    },
    'axisX': {
      'includeZero': false,
      'titleFontSize': 0x0,
      'labelFontSize': 0x0,
      'gridThickness': 0x0,
      'tickLength': 0x0,
      'lineThickness': 0x1
    },
    'axisY': {
      'includeZero': false,
      'labelFontSize': 0xa,
      'gridThickness': 0x0,
      'lineThickness': 0x1
    },
    'legend': {
      'cursor': "pointer",
      'verticalAlign': "top",
      'horizontalAlign': 'left',
      'dockInsidePlotArea': true
    },
    'data': [{
      'type': "line",
      'name': "Price",
      'showInLegend': true,
      'lineColor': "#67636a",
      'lineThickness': 0x1,
      'markerType': 'none',
      'dataPoints': dps
    }, {
      'type': "scatter",
      'name': "Signals",
      'showInLegend': false,
      'indexLabelTextAlign': 'left',
      'markerSize': 0xc,
      'dataPoints': []
    }]
  });
  chart2 = new CanvasJS.Chart("chartContainerDigit", {
    'animationEnabled': false,
    'theme': "light2",
    'title': {
      'titleFontSize': 0x0,
      'text': ''
    },
    'toolTip': {
      'enabled': true,
      'animationEnabled': true,
      'borderColor': "#ccc",
      'borderThickness': 0x1,
      'fontColor': "#000",
      'content': "{y}"
    },
    'axisX': {
      'includeZero': false,
      'titleFontSize': 0x0,
      'labelFontSize': 0x0,
      'gridThickness': 0x0,
      'tickLength': 0x0,
      'lineThickness': 0x1
    },
    'axisY': {
      'stripLines': [{
        'startValue': -0.05,
        'endValue': 0.05,
        'color': 'black'
      }],
      'valueFormatString': "#000",
      'includeZero': false,
      'titleFontSize': 0x0,
      'labelFontSize': 0x0,
      'gridThickness': 0x0,
      'tickLength': 0x0,
      'lineThickness': 0x1
    },
    'data': [{
      'type': "line",
      'lineColor': '#ccc',
      'lineThickness': 0x1,
      'markerType': "none",
      'markerSize': 0x6,
      'markerBorderThickness': 0x0,
      'dataPoints': dps2
    }]
  });
}, false);
function AutoPuTT() {
  var _0x40f43d = Number(document.getElementById("stakeinp_auto").value);
  var _0x1db4a0 = _0x40f43d.toFixed(0x2);
  sessionStorage.modalOrder = _0x1db4a0;
  ws.send(JSON.stringify({
    'buy': '1',
    'price': _0x1db4a0,
    'parameters': {
      'amount': _0x1db4a0,
      'basis': 'stake',
      'contract_type': "PUT",
      'currency': currency,
      'duration': 0x5,
      'duration_unit': 't',
      'symbol': rnd
    }
  }));
}
function AutoCaLL() {
  var _0x1fcc92 = Number(document.getElementById('stakeinp_auto').value);
  var _0x36d298 = _0x1fcc92.toFixed(0x2);
  sessionStorage.modalOrder = _0x36d298;
  ws.send(JSON.stringify({
    'buy': '1',
    'price': _0x36d298,
    'parameters': {
      'amount': _0x36d298,
      'basis': "stake",
      'contract_type': 'CALL',
      'currency': currency,
      'duration': 0x5,
      'duration_unit': 't',
      'symbol': rnd
    }
  }));
}
const navbarMenu = document.getElementById("navbar");
const burgerMenu = document.getElementById("burger");
const overlayMenu = document.querySelector(".overlay");
const toggleMenu = () => {
  navbarMenu.classList.toggle("active");
  overlayMenu.classList.toggle("active");
};
const collapseSubMenu = () => {
  navbarMenu.querySelector(".menu-dropdown.active .submenu").removeAttribute("style");
  navbarMenu.querySelector(".menu-dropdown.active").classList.remove("active");
};
const toggleSubMenu = _0x8c10da => {
  if (_0x8c10da.target.hasAttribute("data-toggle") && window.innerWidth <= 0x3e0) {
    _0x8c10da.preventDefault();
    const _0x5c780e = _0x8c10da.target.parentElement;
    if (_0x5c780e.classList.contains("active")) {
      collapseSubMenu();
    } else {
      if (navbarMenu.querySelector('.menu-dropdown.active')) {
        collapseSubMenu();
      }
      _0x5c780e.classList.add("active");
      const _0x468c4b = _0x5c780e.querySelector('.submenu');
      _0x468c4b.style.maxHeight = _0x468c4b.scrollHeight + 'px';
    }
  }
};
const resizeWindow = () => {
  if (window.innerWidth > 0x3e0) {
    if (navbarMenu.classList.contains("active")) {
      toggleMenu();
    }
    if (navbarMenu.querySelector(".menu-dropdown.active")) {
      collapseSubMenu();
    }
  }
};
burgerMenu.addEventListener("click", toggleMenu);
overlayMenu.addEventListener("click", toggleMenu);
navbarMenu.addEventListener("click", toggleSubMenu);
window.addEventListener("resize", resizeWindow);
function loginclick() {
  window.location.href = '../login';
}
function logiout() {
  setCookie("api_token", '', 0x1e);
  window.location.href = "/ldp";
}
function setCookie(_0x38b2cd, _0x2703c4, _0x474596) {
  var _0x1ee5f2 = '';
  if (_0x474596) {
    var _0x2fd2a7 = new Date();
    _0x2fd2a7.setTime(_0x2fd2a7.getTime() + _0x474596 * 0x18 * 0x3c * 0x3c * 0x3e8);
    _0x1ee5f2 = "; expires=" + _0x2fd2a7.toUTCString();
  }
  document.cookie = _0x38b2cd + '=' + (_0x2703c4 || '') + _0x1ee5f2 + "; path=/";
}