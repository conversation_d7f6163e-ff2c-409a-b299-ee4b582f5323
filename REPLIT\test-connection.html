<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Connection Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="log"></div>
    
    <button onclick="testSymbol('R_10')">Test R_10</button>
    <button onclick="testSymbol('R_25')">Test R_25</button>
    <button onclick="testSymbol('R_50')">Test R_50</button>
    
    <script>
        const API_TOKEN = 'lX8QAY83KmTSa4P';
        let websocket;
        let currentSymbol = 'R_100';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }
        
        function connect() {
            websocket = new WebSocket('wss://ws.derivws.com/websockets/v3?app_id=21317');
            
            websocket.onopen = function() {
                log('WebSocket connected');
                document.getElementById('status').textContent = 'Connected';
                
                // Authorize
                websocket.send(JSON.stringify({
                    'authorize': API_TOKEN
                }));
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                log('Received: ' + JSON.stringify(data));
                
                if (data.msg_type === 'authorize') {
                    if (data.error) {
                        log('Authorization failed: ' + data.error.message);
                    } else {
                        log('Authorized successfully');
                        // Subscribe to initial symbol
                        subscribeToSymbol(currentSymbol);
                    }
                }
                
                if (data.tick) {
                    log('Tick for ' + data.echo_req.ticks + ': ' + data.tick.quote);
                }
                
                if (data.history) {
                    log('History for ' + data.echo_req.ticks_history + ': ' + data.history.prices.length + ' prices');
                }
                
                if (data.error) {
                    log('Error: ' + data.error.message);
                }
            };
            
            websocket.onerror = function(error) {
                log('WebSocket error: ' + error);
            };
            
            websocket.onclose = function() {
                log('WebSocket closed');
                document.getElementById('status').textContent = 'Disconnected';
            };
        }
        
        function subscribeToSymbol(symbol) {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                log('Subscribing to ' + symbol);
                currentSymbol = symbol;
                
                // Forget all previous subscriptions
                websocket.send(JSON.stringify({
                    'forget_all': 'ticks'
                }));
                
                // Subscribe to new symbol
                setTimeout(() => {
                    websocket.send(JSON.stringify({
                        'ticks': symbol
                    }));
                }, 100);
            }
        }
        
        function testSymbol(symbol) {
            log('Testing symbol: ' + symbol);
            subscribeToSymbol(symbol);
        }
        
        // Start connection
        connect();
    </script>
</body>
</html>
