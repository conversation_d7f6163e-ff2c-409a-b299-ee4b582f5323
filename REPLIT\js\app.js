/**
 * LDP Binary Analyzer - Main Application Logic
 * Deobfuscated and cleaned version of app.ldp.min.js
 * 
 * This file contains the main application logic including:
 * - WebSocket connection to Deriv API
 * - Real-time data processing and chart updates
 * - Trading functions for different contract types
 * - User interface management
 * - Auto-trading functionality
 */

// WebSocket configuration
var websocketUrls = ['wss://ws.derivws.com/websockets/v3?app_id=21317'];
var websocket;
var responseData;
var randomIndex;
var spotPrices;
var timestamps;
var chartDataPoints;
var digitChartDataPoints;
var decimalPlaces;
var digitValues;
var randomValue;
var subscriptionId;
var symbolNames;
var chart;
var xValue;
var yValue;
var markerType;
var markerColor;
var randomMenuElements;

// Symbol configuration
symbolNames = ["R_100", "R_10", "R_25", 'R_50', "R_75", "1HZ10V", "1HZ25V", "1HZ50V", '1HZ75V', "1HZ100V"];
var thickValues = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
chartDataPoints = [];
digitChartDataPoints = [];
timestamps = [0];
spotPrices = [0];
var tickValues = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
digitValues = [0];
markerType = "none";
markerColor = "#32cd32";
xValue = 0;
yValue = 0;

// Trading configuration
var currency = "USD";
var isLoading = true;
var startingBalance = 0;
var lastContractId = '';
var AUTO_TRADING_START = false;
var IS_CALL_TRADE = true;
var lastTradeWon = true;
var IS_OPEN = false;

// Notification system configuration
const notyf = new Notyf({
    'duration': 2500, // 0x9c4 = 2500ms
    'position': {
        'x': 'left',
        'y': "top"
    },
    'types': [{
        'type': "error",
        'background': 'indianred',
        'dismissible': true
    }, {
        'type': "warning",
        'background': "#ff9800",
        'dismissible': true,
        'icon': {
            'className': "fa fa-exclamation-triangle notiy-icon",
            'tagName': 'i',
            'text': ''
        }
    }, {
        'type': "info",
        'background': "#29abe2",
        'dismissible': true,
        'icon': {
            'className': "fa fa-info-circle notiy-icon",
            'tagName': 'i',
            'text': ''
        }
    }, {
        'type': "success",
        'background': "#3dc663",
        'dismissible': true
    }]
});

/**
 * Shows notification to user
 * @param {string} type - Notification type (error, warning, info, success)
 * @param {string} message - Message to display
 */
function showNotify(type, message) {
    notyf.open({
        'type': type,
        'message': message
    });
}

// Initialize menu elements
randomMenuElements = document.querySelectorAll("#markt-menu > span");

// Main application initialization
window.addEventListener('load', function () {
    // Add click event listeners to menu elements
    for (var i = 0; i < randomMenuElements.length; i++) {
        addMenuClickListener(randomMenuElements[i]);
    }
    
    /**
     * Adds click event listener to menu element
     * @param {Element} element - Menu element to add listener to
     */
    function addMenuClickListener(element) {
        element.addEventListener("click", function () {
            selectMenuElement(element);
        });
    }

    /**
     * Handles menu element selection and symbol switching
     * @param {Element} selectedElement - The clicked menu element
     */
    function selectMenuElement(selectedElement) {
        // Remove active class from all menu elements
        var allMenuElements = document.querySelectorAll("#markt-menu > span");
        for (var i = 0; i < allMenuElements.length; i++) {
            allMenuElements[i].classList.remove("menu-active");
        }

        // Add active class to selected element
        selectedElement.classList.add("menu-active");

        // Update symbol configuration
        getCurrentSymbolConfig();
        console.log("Symbol changed to:", randomIndex, "with", decimalPlaces, "decimal places");

        // Update current symbol display
        var symbolDisplay = document.getElementById("currentSymbol");
        if (symbolDisplay) {
            symbolDisplay.textContent = "Current Market: " + randomIndex;
        }

        // If WebSocket is connected, switch to new symbol
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            console.log("Switching from", subscriptionId, "to", randomIndex);

            // Unsubscribe from current symbol
            if (subscriptionId) {
                websocket.send(JSON.stringify({
                    'forget': subscriptionId
                }));
                console.log("Unsubscribed from:", subscriptionId);
            }

            // Forget all tick subscriptions
            websocket.send(JSON.stringify({
                'forget_all': 'ticks'
            }));
            console.log("Forgot all ticks");

            // Clear existing chart data
            chartDataPoints = [];
            digitChartDataPoints = [];
            if (chart && chart.options && chart.options.data) {
                chart.options.data[0].dataPoints = [];
                chart.options.data[1].dataPoints = [];
                chart.render();
            }
            if (chart2 && chart2.options && chart2.options.data) {
                chart2.options.data[0].dataPoints = [];
                chart2.render();
            }

            // Subscribe to new symbol with a small delay to ensure unsubscription is processed
            setTimeout(function() {
                console.log("Subscribing to new symbol:", randomIndex);
                websocket.send(JSON.stringify({
                    'ticks': randomIndex
                }));

                // Log the symbol change
                var timestamp = " " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ";
                addLogMessage("<span style=\"color: #3f51b5;\">" + timestamp + "Switching to market (" + randomIndex + ")</span>");
            }, 100);
        }
    }
    
    /**
     * Updates digit display animation based on price movement
     * @param {number} position - Position in the digits array (1-based)
     * @param {string} direction - Direction of movement ('up', 'down', 'mid')
     */
    function updateDigitAnimation(position, direction) {
        var currentClass = document.querySelector("#digits > span:nth-child(" + position + ')').className;
        if (currentClass != 'digits_moved_' + direction) {
            document.querySelector("#digits > span:nth-child(" + position + ')').classList.remove(currentClass);
            document.querySelector("#digits > span:nth-child(" + position + ')').classList.add("digits_moved_" + direction);
        }
    }
    
    /**
     * Updates header column animation based on price movement
     * @param {number} position - Position in the header array (1-based)
     * @param {string} direction - Direction of movement ('up', 'down', 'mid')
     */
    function updateHeaderAnimation(position, direction) {
        var currentClass = document.querySelector("#headcol > span:nth-child(" + position + ')').className;
        if (currentClass != "Head_moved_" + direction) {
            document.querySelector("#headcol > span:nth-child(" + position + ')').classList.remove(currentClass);
            document.querySelector("#headcol > span:nth-child(" + position + ')').classList.add("Head_moved_" + direction);
        }
    }
    
    /**
     * Gets the currently selected trading symbol and sets global variables
     */
    function getCurrentSymbolConfig() {
        randomValue = document.querySelector("#markt-menu > span.menu-active").title;
        switch (randomValue) {
            case symbolNames[0]: // R_100
                randomIndex = "R_100";
                decimalPlaces = 2;
                break;
            case symbolNames[1]: // R_10
                randomIndex = "R_10";
                decimalPlaces = 3;
                break;
            case symbolNames[2]: // R_25
                randomIndex = "R_25";
                decimalPlaces = 3;
                break;
            case symbolNames[3]: // R_50
                randomIndex = "R_50";
                decimalPlaces = 4;
                break;
            case symbolNames[4]: // R_75
                randomIndex = "R_75";
                decimalPlaces = 4;
                break;
            case symbolNames[5]: // 1HZ10V
                randomIndex = "1HZ10V";
                decimalPlaces = 2;
                break;
            case symbolNames[6]: // 1HZ25V
                randomIndex = "1HZ25V";
                decimalPlaces = 2;
                break;
            case symbolNames[7]: // 1HZ50V
                randomIndex = "1HZ50V";
                decimalPlaces = 2;
                break;
            case symbolNames[8]: // 1HZ75V
                randomIndex = "1HZ75V";
                decimalPlaces = 2;
                break;
            case symbolNames[9]: // 1HZ100V
                randomIndex = "1HZ100V";
                decimalPlaces = 2;
                break;
            default:
                randomIndex = 'R';
                decimalPlaces = 0;
                break;
        }
    }

    // Initialize symbol configuration
    getCurrentSymbolConfig();

    // Update initial symbol display
    var symbolDisplay = document.getElementById("currentSymbol");
    if (symbolDisplay) {
        symbolDisplay.textContent = "Current Market: " + randomIndex;
    }

    // Initialize WebSocket connection
    websocket = new WebSocket(websocketUrls[0]);
    var output = document.getElementById("output");
    var timestampPrefix = '';

    /**
     * Authorizes the WebSocket connection with API token
     * @param {string} token - API token for authorization
     */
    function authorizeConnection(token) {
        timestampPrefix = " " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ";
        addLogMessage(timestampPrefix + "Try to Connecting...");
        websocket.send(JSON.stringify({
            'authorize': token
        }));
    }

    // WebSocket connection opened
    websocket.onopen = function (event) {
        websocket.send(JSON.stringify({
            'ticks': randomIndex
        }));
    };

    /**
     * Adds a log message to the output console
     * @param {string} message - Message to add
     * @param {string} borderType - Border type ('t' for top, 'b' for bottom)
     */
    function addLogMessage(message, borderType) {
        var messageDiv = document.createElement("div");
        messageDiv.style.wordWrap = "break-word";
        messageDiv.style.fontSize = "97%";
        if (borderType == 't') {
            messageDiv.style.borderTop = "thin dotted #000000";
        }
        if (borderType == 'b') {
            messageDiv.style.borderBottom = "thin dotted #000000";
        }
        messageDiv.innerHTML = message;
        output.appendChild(messageDiv);
        output.insertBefore(messageDiv, output.childNodes[0]);
    }

    // WebSocket message handler
    websocket.onmessage = function (messageEvent) {
        responseData = JSON.parse(messageEvent.data);
        console.log("WebSocket message received:", responseData);

        // Handle tick data
        if (responseData.tick) {
            getCurrentSymbolConfig();
            console.log("Tick received for:", responseData.echo_req.ticks, "Current symbol:", randomIndex);

            if (responseData.echo_req.ticks == randomIndex) {
                subscriptionId = responseData.tick.id;
                console.log("Requesting history for:", randomIndex);
                websocket.send(JSON.stringify({
                    'ticks_history': randomIndex,
                    'end': "latest",
                    'start': 1,
                    'style': "ticks",
                    'count': 21
                }));
            } else {
                console.log("Symbol mismatch - received:", responseData.echo_req.ticks, "expected:", randomIndex);
                // Unsubscribe from old symbol and subscribe to new one
                websocket.send(JSON.stringify({
                    'forget': subscriptionId
                }));
                websocket.send(JSON.stringify({
                    'forget_all': 'ticks'
                }));
                websocket.send(JSON.stringify({
                    'ticks': randomIndex
                }));
                addLogMessage("<span style=\"color: #3f51b5;\">" + timestampPrefix + "Connecting to market (" + randomIndex + ")</span>");
            }
        }

        // Handle historical data
        if (responseData.history) {
            console.log("History received for:", responseData.echo_req.ticks_history, "Current symbol:", randomIndex);
            if (responseData.echo_req.ticks_history == randomIndex) {
                console.log("Processing history data for", randomIndex);
                // Clean up chart watermarks
                if (document.querySelector("#chartContainer > div > a")) {
                    if (document.querySelector("#chartContainer > div > a").innerText != "            ") {
                        document.querySelector("#chartContainer > div > a").innerHTML = "            ";
                        document.querySelector("#chartContainer > div > a").href = "https://www.binarybot.live";
                    }
                }
                if (document.querySelector("#chartContainerDigit > div > a")) {
                    if (document.querySelector("#chartContainerDigit > div > a").innerText != "        ") {
                        document.querySelector("#chartContainerDigit > div > a").innerHTML = "          ";
                        document.querySelector("#chartContainerDigit > div > a").href = 'https://www.binarybot.live';
                    }
                }

                // Process historical data (reverse order to get latest first)
                for (var dataIndex = 0; dataIndex < 21; dataIndex++) {
                    timestamps[dataIndex] = responseData.history.times[20 - dataIndex]; // 0x14 = 20
                    spotPrices[dataIndex] = responseData.history.prices[20 - dataIndex];
                    spotPrices[dataIndex] = Number(spotPrices[dataIndex]).toFixed(decimalPlaces);
                    digitValues[dataIndex] = spotPrices[dataIndex].slice(-1);
                }

                // Prepare chart data points
                for (var dataIndex = 0; dataIndex < 21; dataIndex++) {
                    xValue = new Date(timestamps[dataIndex] * 1000); // 0x3e8 = 1000
                    yValue = parseFloat(spotPrices[dataIndex]);
                    if (dataIndex == 0) {
                        markerType = 'circle';
                    } else {
                        markerType = 'none';
                    }

                    // Set marker color based on high/low values
                    if (yValue == Math.max.apply(null, spotPrices)) {
                        markerColor = "#29abe2"; // Blue for high
                        markerType = "circle";
                    } else if (yValue == Math.min.apply(null, spotPrices)) {
                        markerColor = "#c03"; // Red for low
                        markerType = "circle";
                    } else {
                        markerColor = "#32cd32"; // Green for normal
                    }

                    chartDataPoints.push({
                        'x': xValue,
                        'y': yValue,
                        'markerType': markerType,
                        'markerColor': markerColor,
                        'markerBorderColor': "#ccc"
                    });
                }

                // Keep only the last 21 data points
                if (chartDataPoints.length > 21) {
                    while (chartDataPoints.length != 21) {
                        chartDataPoints.shift();
                    }
                }

                chart.render();
                spotPrices.reverse();
                digitValues.reverse();
                updateChart(); // Call AI analysis function

                // Process digits and update UI
                for (var digitIndex = 1; digitIndex < 21; digitIndex++) {
                    document.querySelector("#digits > span:nth-child(" + digitIndex + ')').innerHTML = digitValues[digitIndex];
                    var currentPrice = parseFloat(spotPrices[20]); // 0x14 = 20

                    // Determine price movement direction for header styling
                    if (currentPrice == Math.max.apply(null, spotPrices)) {
                        var priceDirection = 'up';
                        var headerColor = "#29abe2";
                    } else if (currentPrice == Math.min.apply(null, spotPrices)) {
                        var priceDirection = "down";
                        headerColor = '#c03';
                    } else {
                        var priceDirection = 'mid';
                        headerColor = "#32cd32";
                    }

                    // Calculate digit movement and update animations
                    if (spotPrices[digitIndex - 1] < spotPrices[digitIndex]) {
                        updateDigitAnimation(digitIndex, 'up');
                        if (digitValues[digitIndex] != 0) {
                            var digitMovement = digitValues[digitIndex] * 1;
                        }
                        if (digitValues[digitIndex - 1] > 5 && digitValues[digitIndex] == 0) {
                            var digitMovement = 10; // 0xa = 10
                        }
                        if (digitValues[digitIndex - 1] <= 5 && digitValues[digitIndex] == 0) {
                            var digitMovement = 0;
                        }
                    } else if (spotPrices[digitIndex - 1] > spotPrices[digitIndex]) {
                        updateDigitAnimation(digitIndex, 'down');
                        if (digitValues[digitIndex] != 0) {
                            var digitMovement = digitValues[digitIndex] * -1;
                        }
                        if (digitValues[digitIndex - 1] > 5 && digitValues[digitIndex] == 0) {
                            var digitMovement = -10; // -0xa = -10
                        }
                        if (digitValues[digitIndex - 1] <= 5 && digitValues[digitIndex] == 0) {
                            var digitMovement = 0; // -0x0 = 0
                        }
                    } else if (spotPrices[digitIndex - 1] == spotPrices[digitIndex] && digitIndex - 1 > 0) {
                        // Handle equal prices by copying previous movement
                        if (document.querySelector("#digits > span:nth-child(" + (digitIndex - 1) + ')').className == "digits_moved_up") {
                            updateDigitAnimation(digitIndex, 'up');
                        } else if (document.querySelector("#digits > span:nth-child(" + (digitIndex - 1) + ')').className == "digits_moved_down") {
                            updateDigitAnimation(digitIndex, "down");
                        }
                    }

                    tickValues.shift(0);
                    tickValues.push(digitMovement);
                }

                var chartColor = "#29abe2";
                thickValues.shift(0);
                thickValues.push(priceDirection);

                // Update digit chart data
                for (var digitIndex = 1; digitIndex < 21; digitIndex++) {
                    if (spotPrices[digitIndex - 1] < spotPrices[digitIndex]) {
                        updateDigitAnimation(digitIndex, 'up');
                        chartColor = '#29abe2';
                    } else if (spotPrices[digitIndex - 1] > spotPrices[digitIndex]) {
                        updateDigitAnimation(digitIndex, 'down');
                        chartColor = "#c03";
                    }
                    updateHeaderAnimation(digitIndex, thickValues[digitIndex - 1]);
                    document.querySelector("#headcol > span:nth-child(" + digitIndex + ')').innerHTML = tickValues;
                    var xDigit = digitIndex;
                    var yDigit = parseFloat(tickValues[digitIndex - 1]);

                    digitChartDataPoints.push({
                        'x': xDigit,
                        'y': yDigit,
                        'indexLabel': digitValues[digitIndex],
                        'indexLabelFontWeight': "bold",
                        'indexLabelFontSize': 11, // 0xb = 11
                        'markerType': "circle",
                        'markerColor': chartColor,
                        'markerBorderColor': "#ccc"
                    });
                }

                // Keep only the last 20 digit chart points
                if (digitChartDataPoints.length > 21) {
                    while (digitChartDataPoints.length != 20) { // 0x14 = 20
                        digitChartDataPoints.shift();
                    }
                }

                chart2.render();

                // Store recent tick values for analysis
                var tick1 = tickValues[19]; // 0x13 = 19
                var tick2 = tickValues[18]; // 0x12 = 18
                var tick3 = tickValues[17]; // 0x11 = 17
                var tick4 = tickValues[16]; // 0x10 = 16
                var tick5 = tickValues[15]; // 0xf = 15
                var tick6 = tickValues[14]; // 0xe = 14

                // Handle initial loading completion
                if (isLoading) {
                    document.getElementById("preloadingtxt").style.display = "none";
                    isLoading = false;
                    if (getCookie("api_token")) {
                        setTimeout(function () {
                            authorizeConnection(getCookie("api_token"));
                        }, 1000); // 0x3e8 = 1000ms
                    } else {
                        addLogMessage("<span style=\"color: #000000;\">" + timestampPrefix + "Please log in to your Deriv account to start trading..</span>", 't');
                        document.getElementById("logout-btn").style.display = "none";
                        document.getElementById("login-btn").style.display = "block";
                        document.getElementById("mobile-login").style.display = "block";
                        document.getElementById("btm-tool").style.display = 'block';
                        document.getElementById("startbtn_auto").classList.add('disabledbtn');
                        openCity("Auto");
                    }
                }
            }
        }

        // Handle different message types
        var messageType = responseData.msg_type;

        // Handle errors
        if (responseData.error && responseData.error.code != 'AlreadySubscribed') {
            addLogMessage("<span style=\"color: red;\"> " + (" " + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + " | ") + '' + responseData.error.message + "</span>", 'b');
            if (AUTO_TRADING_START) {
                stopAutoTrading();
            }
        }

        // Handle authorization response
        if (messageType == "authorize") {
            if (responseData.error) {
                addLogMessage("<span style=\"color: #ffffff;\">" + timestampPrefix + "Please log in to your Deriv account to start trading..</span>", 't');
                document.getElementById("login-btn").style.display = 'block';
                document.getElementById("mobile-login").style.display = 'block';
                document.getElementById("btm-tool").style.display = "block";
                openCity("Auto");
            } else {
                var balance = responseData.authorize.balance;
                var email = responseData.authorize.email;
                currency = responseData.authorize.currency;
                sessionStorage.balanceStarting = balance;
                startingBalance = balance;
                addLogMessage("<span style=\"color: #4caf50;\">" + timestampPrefix + "Connected To The Deriv Market " + randomIndex + "</span>", 't');
                chart.options.data[1].dataPoints = [];
                var fullName = responseData.authorize.fullname;
                var isVirtual = responseData.authorize.is_virtual;
                var loginId = responseData.authorize.loginid;
                sessionStorage.fullnameSaved = fullName;
                sessionStorage.cr_accountSaved = loginId;
                sessionStorage.virtual_accSaved = isVirtual;
                sessionStorage.emailSaved = email;
                sessionStorage.accountType = isVirtual == '0' ? "RealAccount" : 'VirtualAccount';
                sessionStorage.balanceNow = Number(balance);
                document.getElementById("displayBal").innerHTML = "Balance: " + sessionStorage.balanceNow + " " + currency;
                document.getElementById("accountdata").innerHTML = "Account: " + sessionStorage.cr_accountSaved;
                document.getElementById("logout-btn").style.display = "block";
                document.getElementById("btm-tool").style.display = "block";
                document.getElementById("login-btn").style.display = "none";
                document.getElementById("mobile-login").style.display = 'none';
                openCity("Auto");
                subscribeToBalance();
            }
        }
        // Handle balance updates
        else if (messageType == "balance") {
            var newBalance = responseData.balance?.["balance"];
            var previousBalance = sessionStorage.balanceNow;
            sessionStorage.balanceNow = Number(newBalance);
            var balanceChange = newBalance - previousBalance;
            var profit = newBalance - startingBalance;

            if (balanceChange > 0) {
                addLogMessage("<span style=\"color: #4CAF50;\"> " + timestampPrefix + '' + lastContractId + " PROFIT (" + Math.abs(balanceChange).toFixed(2) + ") " + currency + "</span>");
                lastTradeWon = true;
            } else if (balanceChange < 0) {
                lastTradeWon = false;
                addLogMessage("<span style=\"color: #f20202;\"> " + timestampPrefix + '' + lastContractId + " LOSS (" + Math.abs(balanceChange).toFixed(2) + ") " + currency + "</span>");
            }
            updateBalanceDisplay(newBalance);
        }
        // Handle buy confirmation
        else if (messageType == "buy") {
            IS_OPEN = true;
            setTradingIndicator(1);
            var buyResponse = responseData.buy;
            lastContractId = buyResponse.contract_id;
            addLogMessage("<span style=\"color: #122212;\"> " + timestampPrefix + '' + lastContractId + ": (" + buyResponse.buy_price + ") " + buyResponse.longcode + "</span>");
        }
    };

    // Essential utility functions
    function updateBalanceDisplay(newBalance) {
        IS_OPEN = false;
        document.getElementById("displayBal").innerHTML = "Balance: " + newBalance + " " + currency;
        var profit = newBalance - startingBalance;
        var color = profit > 0 ? '#4CAF50' : profit == 0 ? "black" : "#f20202";
        document.getElementById('displayProfitldp').innerHTML = "<span style=\"color: " + color + ";\"> Profit: " + profit.toFixed(2) + " " + currency + "</span>";

        if (AUTO_TRADING_START) {
            IS_CALL_TRADE = true;
            if (profit >= sessionStorage.targetProft) {
                AUTO_TRADING_START = false;
                stopAutoTrading();
            } else if (Math.abs(profit) >= sessionStorage.stopLoss) {
                AUTO_TRADING_START = false;
                stopAutoTrading();
            }
        }
    }

    function setTradingIndicator(mode) {
        if (mode == 0) {
            document.getElementById("live-indicator-analyzing").style.display = "inline-block";
            document.getElementById("live-indicator-buy").style.display = "none";
        } else if (mode == 1) {
            document.getElementById("live-indicator-analyzing").style.display = 'none';
            document.getElementById('live-indicator-buy').style.display = "inline-block";
        } else {
            document.getElementById('live-indicator-analyzing').style.display = "none";
            document.getElementById("live-indicator-buy").style.display = "none";
        }
    }

    function stopAutoTrading() {
        setTradingIndicator(3);
        addLogMessage("<span style=\"color: #625959;\"> " + timestampPrefix + "Trading Stopped. </span>", 'b');
        document.getElementById('stakeinp_auto').value = 1;
    }

    function subscribeToBalance() {
        websocket.send(JSON.stringify({
            'balance': 1,
            'subscribe': 1
        }));
    }

    function getCookie(cookieName) {
        var cookieMatch = document.cookie.match("(^|;) ?" + cookieName + "=([^;]*)(;|$)");
        return cookieMatch ? cookieMatch[2] : null;
    }

    // Initialize charts
    chart = new CanvasJS.Chart('chartContainer', {
        'animationEnabled': false,
        'theme': "light2",
        'title': { 'titleFontSize': 0, 'text': '' },
        'toolTip': { 'enabled': true, 'content': "{name}: {y}" },
        'axisX': { 'includeZero': false, 'labelFontSize': 0, 'gridThickness': 0 },
        'axisY': { 'includeZero': false, 'labelFontSize': 10, 'gridThickness': 0 },
        'data': [{
            'type': "line", 'name': "Price", 'showInLegend': true,
            'lineColor': "#67636a", 'dataPoints': chartDataPoints
        }, {
            'type': "scatter", 'name': "Signals", 'showInLegend': false,
            'markerSize': 12, 'dataPoints': []
        }]
    });

    chart2 = new CanvasJS.Chart("chartContainerDigit", {
        'animationEnabled': false,
        'theme': "light2",
        'title': { 'titleFontSize': 0, 'text': '' },
        'toolTip': { 'enabled': true, 'content': "{y}" },
        'axisX': { 'includeZero': false, 'labelFontSize': 0, 'gridThickness': 0 },
        'axisY': { 'includeZero': false, 'labelFontSize': 0, 'gridThickness': 0 },
        'data': [{ 'type': "line", 'lineColor': '#ccc', 'dataPoints': digitChartDataPoints }]
    });

}, false); // End of window load event listener

/**
 * Test function to verify all volatility indices are selectable
 * Call this function in browser console: testAllVolatilityIndices()
 */
function testAllVolatilityIndices() {
    console.log("Testing all volatility indices...");
    var allSymbols = ["R_100", "R_10", "R_25", "R_50", "R_75", "1HZ10V", "1HZ25V", "1HZ50V", "1HZ75V", "1HZ100V"];
    var menuElements = document.querySelectorAll("#markt-menu > span");

    console.log("Found", menuElements.length, "menu elements");

    allSymbols.forEach(function(symbol, index) {
        var menuElement = document.querySelector("#markt-menu > span[title='" + symbol + "']");
        if (menuElement) {
            console.log("✅", symbol, "- Menu element found");

            // Test click functionality
            menuElement.click();
            setTimeout(function() {
                if (randomIndex === symbol) {
                    console.log("✅", symbol, "- Click functionality working, decimal places:", decimalPlaces);
                } else {
                    console.log("❌", symbol, "- Click functionality failed");
                }
            }, 100);
        } else {
            console.log("❌", symbol, "- Menu element NOT found");
        }
    });

    console.log("Test completed. Check console for results.");
}

/**
 * Test WebSocket connection with specific API token
 * Call this function in browser console: testWebSocketConnection('your_api_token')
 */
function testWebSocketConnection(apiToken) {
    console.log("Testing WebSocket connection with API token...");

    if (websocket && websocket.readyState === WebSocket.OPEN) {
        console.log("WebSocket is connected");

        // Test authorization
        websocket.send(JSON.stringify({
            'authorize': apiToken || 'lX8QAY83KmTSa4P'
        }));

        console.log("Authorization request sent");
    } else {
        console.log("WebSocket is not connected. Current state:", websocket ? websocket.readyState : "undefined");
    }
}

/**
 * Force subscribe to a specific symbol
 * Call this function in browser console: forceSubscribeToSymbol('R_10')
 */
function forceSubscribeToSymbol(symbol) {
    console.log("Force subscribing to symbol:", symbol);

    if (websocket && websocket.readyState === WebSocket.OPEN) {
        // Update the global variable
        randomIndex = symbol;

        // Forget all current subscriptions
        websocket.send(JSON.stringify({
            'forget_all': 'ticks'
        }));

        // Subscribe to new symbol
        setTimeout(function() {
            websocket.send(JSON.stringify({
                'ticks': symbol
            }));
            console.log("Subscription request sent for:", symbol);
        }, 200);
    } else {
        console.log("WebSocket is not connected");
    }
}

// Auto trading functions called by AI
function AutoPuTT() {
    var stakeAmount = Number(document.getElementById("stakeinp_auto").value);
    var formattedStake = stakeAmount.toFixed(2);
    sessionStorage.modalOrder = formattedStake;
    websocket.send(JSON.stringify({
        'buy': '1', 'price': formattedStake,
        'parameters': {
            'amount': formattedStake, 'basis': 'stake', 'contract_type': "PUT",
            'currency': currency, 'duration': 5, 'duration_unit': 't', 'symbol': randomIndex
        }
    }));
}

function AutoCaLL() {
    var stakeAmount = Number(document.getElementById('stakeinp_auto').value);
    var formattedStake = stakeAmount.toFixed(2);
    sessionStorage.modalOrder = formattedStake;
    websocket.send(JSON.stringify({
        'buy': '1', 'price': formattedStake,
        'parameters': {
            'amount': formattedStake, 'basis': "stake", 'contract_type': 'CALL',
            'currency': currency, 'duration': 5, 'duration_unit': 't', 'symbol': randomIndex
        }
    }));
}

// Utility functions for login/logout
function loginclick() { window.location.href = '../login'; }
function logiout() { setCookie("api_token", '', 30); window.location.href = "/ldp"; }
function setCookie(name, value, days) {
    var expires = '';
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
        expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + '=' + (value || '') + expires + "; path=/";
}
