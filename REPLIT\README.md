# LDP Binary Analyzer - Deobfuscated Version

This is a complete, deobfuscated replica of the LDP WIZLY WIZ project with clean, readable JavaScript code.

## 🚀 What's New

- **Fully Deobfuscated Code**: All JavaScript files have been cleaned and made readable
- **Comprehensive Documentation**: Every function is documented with clear comments
- **Readable Variable Names**: All obfuscated variable names have been replaced with meaningful names
- **Code Structure**: Organized and well-structured codebase

## 📁 Project Structure

```
REPLIT/
├── index.html              # Main HTML file (updated script references)
├── css/                    # Styling files
│   ├── style.css          # Main stylesheet
│   ├── w3.css             # W3.CSS framework
│   └── notyf.min.css      # Notification library styles
├── js/                     # JavaScript files
│   ├── ai.js              # 🆕 Deobfuscated AI trading algorithm
│   ├── app.js             # 🆕 Deobfuscated main application logic
│   ├── canvasjs.min.js    # Chart library (third-party)
│   └── notyf.min.js       # Notification library (third-party)
├── img/                    # Image assets
│   ├── analizing.gif      # Loading animation for analysis
│   ├── loading.gif        # General loading animation
│   └── up.gif             # Buy signal animation
└── README.md              # This documentation file
```

## 🧠 AI Trading Algorithm (ai.js)

The AI trading system includes:

### Technical Indicators
- **Bollinger Bands**: Price volatility and trend analysis
- **ATR (Average True Range)**: Volatility measurement
- **SMA (Simple Moving Average)**: Trend identification
- **Volatility Calculation**: Risk assessment
- **Trend Strength**: Momentum analysis

### Market Condition Analysis
- Real-time market condition classification
- 5-level system: Very Bad, Bad, Neutral, Good, Very Good
- Historical data analysis for pattern recognition
- Adaptive thresholds based on market behavior

### Signal Generation
- **Buy Signals**: Generated when price crosses above lower Bollinger Band
- **Sell Signals**: Generated when price crosses below upper Bollinger Band
- **ATR Confirmation**: Signals validated using Average True Range
- **Visual Indicators**: Green arrows (▲) for buy, red arrows (▼) for sell

## 🔧 Main Application (app.js)

### Core Features
- **WebSocket Connection**: Real-time data from Deriv API
- **Multi-Symbol Support**: R_100, R_10, R_25, R_50, R_75, and 1Hz variants
- **Auto-Trading**: Automated trading based on AI signals
- **Manual Trading**: Support for various contract types
- **Risk Management**: Stop loss and target profit controls

### Trading Contract Types
1. **Rise/Fall**: Traditional binary options
2. **Over/Under**: Digit prediction contracts
3. **Diff/Match**: Last digit difference/match contracts
4. **Auto Trading**: AI-powered automated trading

### Real-time Features
- Live price charts with CanvasJS
- Digit analysis and visualization
- Balance tracking and profit/loss calculation
- Market condition notifications
- Trading history and logs

## 🎯 Key Improvements in Deobfuscated Version

### Code Readability
- **Before**: `_0x53a5ab`, `_0x4ec204`, `_0x2cf5be`
- **After**: `dataPoints`, `bollingerBands`, `atrData`

### Function Documentation
```javascript
/**
 * Calculates Bollinger Bands (moving average with standard deviation bands)
 * @param {Array} dataPoints - Array of price data points
 * @param {number} period - Period for moving average (default: 20)
 * @param {number} stdDev - Standard deviation multiplier (default: 2)
 * @returns {Array} Array of Bollinger Band data with upper, middle, lower values
 */
function calculateBollingerBands(dataPoints, period = 20, stdDev = 2) {
    // Implementation with clear logic
}
```

### Clear Variable Names
- `previousMarketCondition` instead of `previousMarketCondition`
- `volatilityHistory` instead of obfuscated arrays
- `marketConditionHistory` instead of cryptic variables

## 🚀 Getting Started

1. **Open the Project**: Navigate to the REPLIT folder
2. **Launch**: Open `index.html` in a web browser
3. **Connect**: Use your Deriv API token to connect
4. **Trade**: Choose manual trading or enable AI auto-trading

## 🔐 Security Notes

- The deobfuscated code maintains all original security features
- API tokens are handled securely through cookies
- WebSocket connections use official Deriv endpoints
- No malicious code or backdoors introduced

## 📊 AI Algorithm Explanation

### Market Condition Classification
```javascript
// Market conditions are classified based on volatility and trend strength
if (currentVolatility > highVolatilityThreshold && currentTrendStrength < lowTrendStrengthThreshold) {
    return -2; // Very bad market condition (high volatility, weak trend)
} else if (currentVolatility > highVolatilityThreshold) {
    return -1; // Bad market condition (high volatility)
} else if (currentTrendStrength < lowTrendStrengthThreshold) {
    return 0; // Neutral market condition (weak trend)
} else if (currentVolatility < avgVolatility * 0.6 && currentTrendStrength > avgTrendStrength * 1.5) {
    return 2; // Very good market condition (low volatility, strong trend)
} else {
    return currentVolatility < avgVolatility && currentTrendStrength > avgTrendStrength ? 1 : 0;
}
```

### Signal Generation Logic
```javascript
// Buy signal: Price crosses above lower Bollinger Band with ATR confirmation
if (priceData[currentIndex].y > currentBB.lower && 
    priceData[currentIndex - 1].y <= previousBB.lower && 
    priceData[currentIndex].y - currentBB.lower < currentATR) {
    // Generate buy signal
}
```

## 🛠️ Technical Requirements

- Modern web browser with JavaScript enabled
- Internet connection for real-time data
- Deriv account and API token for trading

## ⚠️ Disclaimer

This is an educational project. Trading involves risk, and past performance does not guarantee future results. Always trade responsibly and never risk more than you can afford to lose.

## 📝 License

This deobfuscated version is provided for educational purposes. Please respect the original project's licensing terms.
